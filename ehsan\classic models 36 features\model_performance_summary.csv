model_name,accuracy,precision,recall,f1,r2
AdaBoost,0.9508370284180844,0.9563461841639873,0.9508368762903491,0.9503763263835747,0.01880108676666752
<PERSON> Tree,0.987232302902607,0.9877241023375598,0.9872322585341292,0.9872240592448707,0.9763439460960843
Extra Trees,0.9810720812324636,0.9820360238652096,0.9810720151559125,0.9810517232174935,0.9547375335984792
Gradient Boosting,0.9872462795825165,0.9876431918111418,0.9872462350186939,0.9872395071687411,0.9765514524803424
KNN,0.9846815588191103,0.9848895456140095,0.984681506026232,0.9846673157791518,0.9701563073733371
LightGBM,0.9872532679224714,0.9877023998422612,0.9872532233830672,0.9872455929806471,0.9766379394598494
Logistic Regression,0.5105855879465112,0.4827990517607004,0.510586555481247,0.43337294612856453,0.22135808517750313
Naive Bayes,0.41186130940525734,0.47349998453943903,0.4118610771595317,0.35770888626111663,-0.46576508160029084
Random Forest,0.9871973612028331,0.9878248150814892,0.9871973167122625,0.9871866524996522,0.976321995459281
SGDClassifier,0.8745348386217596,0.8790031315381869,0.8745349318687066,0.8737842364936819,-114141715335492.66
XGBoost,0.9871938670328557,0.9876063970034172,0.9871938223469394,0.9871868850340236,0.9763976335525513
