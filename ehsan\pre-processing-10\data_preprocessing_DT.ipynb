{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Starting data preprocessing pipeline...\n", "Successfully loaded 02-14-2018.csv (Memory: 63.42 MB)\n", "Successfully loaded 02-15-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-16-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-20-2018.csv (Memory: 667.09 MB)\n", "Successfully loaded 02-21-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-22-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-23-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-28-2018.csv (Memory: 51.45 MB)\n", "Successfully loaded 03-01-2018.csv (Memory: 27.79 MB)\n", "Successfully loaded 03-02-2018.csv (Memory: 88.00 MB)\n", "\n", "Processing 02-14-2018...\n", "Dropped 1 rows containing NaN values\n", "Computing DoS feature for 02-14-2018...\n", "Memory usage reduced from 96.84 MB to 65.13 MB (32.74% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Brute-force    380949\n", "Benign         374739\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Brute-force    380949\n", "Benign         374739\n", "Name: count, dtype: int64\n", "No balancing needed as malicious samples exceed or equal benign ones\n", "\n", "02-14-2018 dataset Information:\n", "Shape: (755688, 13)\n", "\n", "Label distribution:\n", "Label\n", "Brute-force    380949\n", "Benign         374739\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    380949\n", "Benign       374739\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 65.13129425048828 MB\n", "\n", "Processing 02-15-2018...\n", "Computing DoS feature for 02-15-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign        996077\n", "DoS attack     52498\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign        996077\n", "DoS attack     52498\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign        52498\n", "DoS attack    52498\n", "Name: count, dtype: int64\n", "\n", "02-15-2018 dataset Information:\n", "Shape: (104996, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign        52498\n", "DoS attack    52498\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       52498\n", "Malicious    52498\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 6.8089752197265625 MB\n", "\n", "Processing 02-16-2018...\n", "Computing DoS feature for 02-16-2018...\n", "Memory usage reduced from 144.25 MB to 132.25 MB (8.32% reduction)\n", "Label distribution after transformation:\n", "Label\n", "DoS attack    601802\n", "Benign        446772\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "DoS attack    601802\n", "Benign        446772\n", "Name: count, dtype: int64\n", "No balancing needed as malicious samples exceed or equal benign ones\n", "\n", "02-16-2018 dataset Information:\n", "Shape: (1048574, 13)\n", "\n", "Label distribution:\n", "Label\n", "DoS attack    601802\n", "Benign        446772\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    601802\n", "Benign       446772\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 132.24984741210938 MB\n", "\n", "Processing 02-20-2018...\n", "Computing DoS feature for 02-20-2018...\n", "Memory usage reduced from 1107.02 MB to 773.48 MB (30.13% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign         7372557\n", "DDoS attack     576191\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign         7372557\n", "DDoS attack     576191\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign         576191\n", "DDoS attack    576191\n", "Name: count, dtype: int64\n", "\n", "02-20-2018 dataset Information:\n", "Shape: (1152382, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign         576191\n", "DDoS attack    576191\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       576191\n", "Malicious    576191\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 74.73180389404297 MB\n", "\n", "Processing 02-21-2018...\n", "Computing DoS feature for 02-21-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "DDoS attack    687742\n", "Benign         360833\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "DDoS attack    687742\n", "Benign         360833\n", "Name: count, dtype: int64\n", "No balancing needed as malicious samples exceed or equal benign ones\n", "\n", "02-21-2018 dataset Information:\n", "Shape: (1048575, 13)\n", "\n", "Label distribution:\n", "Label\n", "DDoS attack    687742\n", "Benign         360833\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    687742\n", "Benign       360833\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 100.24997329711914 MB\n", "\n", "Processing 02-22-2018...\n", "Computing DoS feature for 02-22-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign        1048213\n", "Web attack        362\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign        1048213\n", "Web attack        362\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign        362\n", "Web attack    362\n", "Name: count, dtype: int64\n", "\n", "02-22-2018 dataset Information:\n", "Shape: (724, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign        362\n", "Web attack    362\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       362\n", "Malicious    362\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 0.0469512939453125 MB\n", "\n", "Processing 02-23-2018...\n", "Computing DoS feature for 02-23-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign        1048009\n", "Web attack        566\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign        1048009\n", "Web attack        566\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign        566\n", "Web attack    566\n", "Name: count, dtype: int64\n", "\n", "02-23-2018 dataset Information:\n", "Shape: (1132, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign        566\n", "Web attack    566\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       566\n", "Malicious    566\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 0.0734100341796875 MB\n", "\n", "Processing 02-28-2018...\n", "Computing DoS feature for 02-28-2018...\n", "Memory usage reduced from 81.61 MB to 74.59 MB (8.60% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign           544200\n", "Infilteration     68871\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign           544200\n", "Infilteration     68871\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign           68871\n", "Infilteration    68871\n", "Name: count, dtype: int64\n", "\n", "02-28-2018 dataset Information:\n", "Shape: (137742, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign           68871\n", "Infilteration    68871\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       68871\n", "Malicious    68871\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 13.136100769042969 MB\n", "\n", "Processing 03-01-2018...\n", "Computing DoS feature for 03-01-2018...\n", "Memory usage reduced from 43.43 MB to 39.64 MB (8.73% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign           238037\n", "Infilteration     93063\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign           238037\n", "Infilteration     93063\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign           93063\n", "Infilteration    93063\n", "Name: count, dtype: int64\n", "\n", "03-01-2018 dataset Information:\n", "Shape: (186126, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign           93063\n", "Infilteration    93063\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       93063\n", "Malicious    93063\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 17.75035858154297 MB\n", "\n", "Processing 03-02-2018...\n", "Computing DoS feature for 03-02-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign    762384\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign    762384\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign    286191\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "\n", "03-02-2018 dataset Information:\n", "Shape: (572382, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign    286191\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       286191\n", "Malicious    286191\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 37.11888885498047 MB\n", "\n", "Combining all datasets...\n", "\n", "Combined dataset Information:\n", "Shape: (5008321, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign           2260086\n", "DDoS attack      1263933\n", "DoS attack        654300\n", "Brute-force       380949\n", "Botnet            286191\n", "Infilteration     161934\n", "Web attack           928\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    2748235\n", "Benign       2260086\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 439.4203796386719 MB\n", "\n", "Handling outliers...\n", "Processing outliers using winsorize strategy...\n", "\n", "Selecting top 10 features...\n", "Selected features: ['Conn_<PERSON>', 'Fwd Seg Size Min', 'Bwd Pkt Len Mean', 'Dst Port', 'TotLen Fwd Pkts', 'Init Fwd Win Byts', 'Flow Duration', 'Flow IAT Mean', 'Init Bwd Win Byts', 'Flow IAT Max', 'Label', 'Threat']\n", "\n", "Removing unwanted labels and balancing dataset...\n", "Removed 162862 rows with labels: ['Infilteration', 'Web attack']\n", "Class distribution after balancing:\n", "Label\n", "Benign         286191\n", "Botnet         286191\n", "Brute-force    286191\n", "DDoS attack    286191\n", "DoS attack     286191\n", "Name: count, dtype: int64\n", "\n", "Preparing data for modeling...\n", "Using RobustScaler for scaling...\n", "\n", "Final dataset preparation complete!\n", "\n", "Training dataset Information:\n", "Shape: (1144764, 12)\n", "\n", "Label distribution:\n", "Label\n", "DoS attack     228953\n", "Benign         228953\n", "Botnet         228953\n", "Brute-force    228953\n", "DDoS attack    228952\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    915811\n", "Benign       228953\n", "Name: count, dtype: int64\n", "\n", "Number of features: 10\n", "\n", "Memory usage: 113.54013061523438 MB\n", "\n", "Testing dataset Information:\n", "Shape: (286191, 12)\n", "\n", "Label distribution:\n", "Label\n", "DDoS attack    57239\n", "Benign         57238\n", "Brute-force    57238\n", "DoS attack     57238\n", "Botnet         57238\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    228953\n", "Benign        57238\n", "Name: count, dtype: int64\n", "\n", "Number of features: 10\n", "\n", "Memory usage: 28.385032653808594 MB\n", "\n", "Class weights: {'Benign': np.float64(0.9999991264582687), 'Botnet': np.float64(0.9999991264582687), 'Brute-force': np.float64(0.9999991264582687), 'DDoS attack': np.float64(1.0000034941821867), 'DoS attack': np.float64(0.9999991264582687)}\n", "All artifacts saved to .\\artifacts\n", "\n", "Note: Use StratifiedKFold during modeling for robust validation\n", "\n", "Preprocessing pipeline completed successfully!\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import warnings\n", "import os\n", "from imblearn.under_sampling import RandomUnderSampler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import MinMaxScaler, RobustScaler\n", "from sklearn.utils import class_weight\n", "import joblib\n", "import json\n", "from typing import Dict, Tu<PERSON>, Any\n", "\n", "# Suppress warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "### Data Loading and Initial Preprocessing Functions\n", "\n", "\n", "def load_csv_files(\n", "    base_path: str, date_list: list, chunksize: int = None\n", ") -> Dict[str, pd.DataFrame]:\n", "    \"\"\"\n", "    Load CSV files using only the necessary columns.\n", "\n", "    Required columns include:\n", "      - For computing features: 'Timestamp', 'Dst Port', 'Flow Duration'\n", "      - For final features: 'Fwd Seg Size Min', 'Bwd Pkt Len Mean', 'TotLen Fwd Pkts',\n", "        'Init Fwd Win Byts', 'Flow IAT Mean', 'Init Bwd Win Byts', 'Flow IAT Max'\n", "      - For target: 'Label'\n", "    \"\"\"\n", "    required_cols = [\n", "        \"Timestamp\",\n", "        \"Dst Port\",\n", "        \"Flow Duration\",\n", "        \"Fwd Seg Size Min\",\n", "        \"Bwd Pkt Len Mean\",\n", "        \"TotLen Fwd Pkts\",\n", "        \"Init Fwd Win Byts\",\n", "        \"Flow IAT Mean\",\n", "        \"Init Bwd Win Byts\",\n", "        \"Flow IAT Max\",\n", "        \"Label\",\n", "    ]\n", "\n", "    dataframes = {}\n", "    for date in date_list:\n", "        file_path = os.path.join(base_path, f\"{date}.csv\")\n", "        try:\n", "            if chunksize:\n", "                chunks = []\n", "                for chunk in pd.read_csv(\n", "                    file_path,\n", "                    usecols=required_cols,\n", "                    chunksize=chunksize,\n", "                    low_memory=False,\n", "                ):\n", "                    chunks.append(chunk)\n", "                dataframes[date] = pd.concat(chunks, ignore_index=True)\n", "            else:\n", "                dataframes[date] = pd.read_csv(\n", "                    file_path, usecols=required_cols, low_memory=False\n", "                )\n", "            mem_usage = dataframes[date].memory_usage().sum() / 1024**2\n", "            print(f\"Successfully loaded {date}.csv (Memory: {mem_usage:.2f} MB)\")\n", "        except Exception as e:\n", "            print(f\"Error loading {date}.csv: {str(e)}\")\n", "    return dataframes\n", "\n", "\n", "def preprocess_initial_columns(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Remove any columns that are not necessary.\n", "    (Since we already load only the required columns, this step is minimal.)\n", "    \"\"\"\n", "    return df\n", "\n", "\n", "def clean_infinite_values(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Replace infinite values with NaN and drop rows containing NaNs.\"\"\"\n", "    df = df.replace([\"Infinity\", \"infinity\"], np.inf)\n", "    df = df.replace([np.inf, -np.inf], np.nan)\n", "    initial_rows = len(df)\n", "    df.dropna(inplace=True)\n", "    dropped_rows = initial_rows - len(df)\n", "    if dropped_rows > 0:\n", "        print(f\"Dropped {dropped_rows} rows containing NaN values\")\n", "    return df\n", "\n", "\n", "def drop_timestamp(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Remove the Timestamp column once it's no longer needed.\"\"\"\n", "    if \"Timestamp\" in df.columns:\n", "        df.drop(columns=\"Timestamp\", inplace=True)\n", "    return df\n", "\n", "\n", "### Label Generation and Memory Optimization\n", "\n", "\n", "def generate_binary_label(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Generate binary classification labels.\"\"\"\n", "    df[\"Threat\"] = df[\"Label\"].apply(\n", "        lambda x: \"Benign\" if x == \"Benign\" else \"Malicious\"\n", "    )\n", "    return df\n", "\n", "\n", "def optimize_memory_usage(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Convert numerical columns to float32 to optimize memory usage.\"\"\"\n", "    start_mem = df.memory_usage().sum() / 1024**2\n", "    for col in df.columns:\n", "        if df[col].dtype != object:\n", "            if not np.isfinite(df[col]).all():\n", "                df[col] = df[col].fillna(df[col].min() if not df[col].empty else 0)\n", "            df[col] = df[col].astype(np.float32)\n", "    end_mem = df.memory_usage().sum() / 1024**2\n", "    reduction = 100 * (start_mem - end_mem) / start_mem\n", "    print(\n", "        f\"Memory usage reduced from {start_mem:.2f} MB to {end_mem:.2f} MB ({reduction:.2f}% reduction)\"\n", "    )\n", "    return df\n", "\n", "\n", "def transform_multi_label(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Simplify the label categories.\"\"\"\n", "    mapping = {\n", "        \"SSH-Bruteforce\": \"Brute-force\",\n", "        \"FTP-BruteForce\": \"Brute-force\",\n", "        \"Brute Force -XSS\": \"Web attack\",\n", "        \"Brute Force -Web\": \"Web attack\",\n", "        \"SQL Injection\": \"Web attack\",\n", "        \"DoS attacks-Hulk\": \"DoS attack\",\n", "        \"DoS attacks-SlowHTTPTest\": \"DoS attack\",\n", "        \"DoS attacks-Slowloris\": \"DoS attack\",\n", "        \"DoS attacks-GoldenEye\": \"DoS attack\",\n", "        \"DDOS attack-HOIC\": \"DDoS attack\",\n", "        \"DDOS attack-LOIC-UDP\": \"DDoS attack\",\n", "        \"DDoS attacks-LOIC-HTTP\": \"DDoS attack\",\n", "        \"Bot\": \"Botnet\",\n", "        \"Infilteration\": \"Infilteration\",\n", "        \"Benign\": \"Benign\",\n", "        \"Label\": \"Benign\",\n", "    }\n", "    df[\"Label\"] = df[\"Label\"].map(mapping).fillna(\"Other\")\n", "    print(\"Label distribution after transformation:\")\n", "    print(df[\"Label\"].value_counts())\n", "    return df\n", "\n", "\n", "### Balancing Function\n", "\n", "\n", "def balance_dataset_if_needed(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Balance the dataset if benign samples exceed malicious ones.\n", "    (Uses random under-sampling.)\n", "    \"\"\"\n", "    label_counts = df[\"Label\"].value_counts()\n", "    print(\"Initial class distribution:\")\n", "    print(label_counts)\n", "    benign_count = label_counts.get(\"Benign\", 0)\n", "    malicious_count = label_counts.drop(\"Benign\", errors=\"ignore\").sum()\n", "    if benign_count > malicious_count:\n", "        print(\"Balancing dataset as benign samples exceed malicious ones...\")\n", "        X = df.drop([\"Label\", \"Threat\"], axis=1)\n", "        y = df[\"Label\"]\n", "        rus = RandomUnderSampler(random_state=42)\n", "        X_balanced, y_balanced = rus.fit_resample(X, y)\n", "        df = pd.concat([X_balanced, pd.Series(y_balanced, name=\"Label\")], axis=1)\n", "        df = generate_binary_label(df)\n", "        print(\"Class distribution after balancing:\")\n", "        print(df[\"Label\"].value_counts())\n", "    else:\n", "        print(\"No balancing needed as malicious samples exceed or equal benign ones\")\n", "    return df\n", "\n", "\n", "### DoS-Specific Feature Engineering\n", "\n", "\n", "def add_dos_features(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Compute the 'Conn_Rate' feature needed for the final top features.\n", "    Uses 'Timestamp', 'Dst Port', and 'Flow Duration'.\n", "\n", "    To avoid errors, we first convert 'Flow Duration' to numeric.\n", "    \"\"\"\n", "    df[\"Timestamp\"] = pd.to_datetime(df[\"Timestamp\"], errors=\"coerce\")\n", "    df[\"Flow Duration\"] = pd.to_numeric(df[\"Flow Duration\"], errors=\"coerce\")\n", "    df = df.dropna(subset=[\"Timestamp\", \"Flow Duration\"]).reset_index(drop=True)\n", "    df.sort_values(by=[\"Dst Port\", \"Timestamp\"], inplace=True)\n", "\n", "    # Compute connection rate using a 5-second rolling window\n", "    conn_counts = []\n", "    for _, group in df.groupby(\"Dst Port\"):\n", "        rolling_count = group.rolling(\"5s\", on=\"Timestamp\")[\"Timestamp\"].count()\n", "        conn_counts.append(rolling_count)\n", "    conn_count_series = pd.concat(conn_counts)\n", "    df[\"Conn_Rate\"] = conn_count_series / 5.0\n", "\n", "    # Compute concurrency (extra column, will be dropped later)\n", "    events = []\n", "    for i, row in df.iterrows():\n", "        start_time = row[\"Timestamp\"]\n", "        duration = row[\"Flow Duration\"]\n", "        end_time = start_time + pd.to_timedelta(duration, unit=\"us\")\n", "        events.append((start_time, \"start\", row[\"Dst Port\"], i))\n", "        events.append((end_time, \"end\", row[\"Dst Port\"], i))\n", "    events.sort(key=lambda x: (x[0], x[1]))\n", "    active_flows = {}\n", "    concurrency = pd.Series(0, index=df.index)\n", "    for time, etype, port, idx in events:\n", "        if etype == \"start\":\n", "            concurrency.loc[idx] = active_flows.get(port, 0)\n", "            active_flows[port] = active_flows.get(port, 0) + 1\n", "        else:\n", "            active_flows[port] = active_flows.get(port, 0) - 1\n", "    df[\"Concurrency\"] = concurrency\n", "    return df\n", "\n", "\n", "### Outlier Handling\n", "\n", "\n", "def handle_outliers(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    strategy: str = \"winsorize\",\n", "    threshold: float = 3.0,\n", "    exclude_cols: list = None,\n", ") -> pd.DataFrame:\n", "    \"\"\"Apply outlier handling without removing attack data.\"\"\"\n", "    if exclude_cols is None:\n", "        exclude_cols = [\"Label\", \"Threat\"]\n", "    df_result = df.copy()\n", "    numeric_cols = df_result.select_dtypes(include=[np.number]).columns\n", "    cols_to_process = [col for col in numeric_cols if col not in exclude_cols]\n", "    print(f\"Processing outliers using {strategy} strategy...\")\n", "    for col in cols_to_process:\n", "        z_scores = np.abs(\n", "            (df_result[col] - df_result[col].mean()) / df_result[col].std()\n", "        )\n", "        outliers = z_scores > threshold\n", "        if outliers.sum() == 0:\n", "            continue\n", "        if strategy == \"winsorize\":\n", "            lower_bound = df_result[col].quantile(0.01)\n", "            upper_bound = df_result[col].quantile(0.99)\n", "            df_result.loc[df_result[col] < lower_bound, col] = lower_bound\n", "            df_result.loc[df_result[col] > upper_bound, col] = upper_bound\n", "        elif strategy == \"cap\":\n", "            lower_bound = df_result[col].mean() - (threshold * df_result[col].std())\n", "            upper_bound = df_result[col].mean() + (threshold * df_result[col].std())\n", "            df_result.loc[df_result[col] < lower_bound, col] = lower_bound\n", "            df_result.loc[df_result[col] > upper_bound, col] = upper_bound\n", "        elif strategy == \"log_transform\" and df_result[col].min() >= 0:\n", "            df_result.loc[outliers, col] = np.log1p(df_result.loc[outliers, col])\n", "    return df_result\n", "\n", "\n", "### Scaling Functions\n", "\n", "\n", "def apply_robust_scaling(\n", "    train_df: pd.DataFrame, test_df: pd.DataFrame, use_robust: bool = True\n", ") -> Tuple[pd.DataFrame, pd.DataFrame, object]:\n", "    \"\"\"Apply RobustScaler (or MinMaxScaler) to the feature columns.\"\"\"\n", "    feature_cols = [col for col in train_df.columns if col not in [\"Label\", \"Threat\"]]\n", "    if use_robust:\n", "        print(\"Using RobustScaler for scaling...\")\n", "        scaler = RobustScaler()\n", "    else:\n", "        print(\"Using MinMaxScaler for scaling...\")\n", "        scaler = MinMaxScaler()\n", "    train_df[feature_cols] = scaler.fit_transform(train_df[feature_cols])\n", "    test_df[feature_cols] = scaler.transform(test_df[feature_cols])\n", "    return train_df, test_df, scaler\n", "\n", "\n", "### Final Data Preparation Functions\n", "\n", "\n", "def remove_labels_and_balance(\n", "    df: pd.<PERSON><PERSON>rame, labels_to_remove: list, random_state: int = 42\n", ") -> pd.DataFrame:\n", "    \"\"\"Remove specific labels and balance the dataset.\"\"\"\n", "    initial_count = len(df)\n", "    df = df[~df[\"Label\"].isin(labels_to_remove)]\n", "    removed = initial_count - len(df)\n", "    print(f\"Removed {removed} rows with labels: {labels_to_remove}\")\n", "    min_count = df[\"Label\"].value_counts().min()\n", "    df_balanced = (\n", "        df.groupby(\"Label\")\n", "        .apply(lambda x: x.sample(min_count, random_state=random_state))\n", "        .reset_index(drop=True)\n", "    )\n", "    print(\"Class distribution after balancing:\")\n", "    print(df_balanced[\"Label\"].value_counts())\n", "    return df_balanced\n", "\n", "\n", "def print_dataset_info(df: pd.DataFrame, name: str = \"Dataset\") -> None:\n", "    \"\"\"Print basic information about the dataset.\"\"\"\n", "    print(f\"\\n{name} Information:\")\n", "    print(f\"Shape: {df.shape}\")\n", "    print(\"\\nLabel distribution:\")\n", "    print(df[\"Label\"].value_counts())\n", "    print(\"\\nThreat distribution:\")\n", "    print(df[\"Threat\"].value_counts())\n", "    print(f\"\\nNumber of features: {len(df.columns) - 2}\")\n", "    print(\"\\nMemory usage:\", df.memory_usage().sum() / 1024**2, \"MB\")\n", "\n", "\n", "def save_artifacts(\n", "    train_df: pd.DataFrame,\n", "    test_df: pd.DataFrame,\n", "    class_weights: Dict,\n", "    scaler: Any,\n", "    output_dir: str,\n", ") -> None:\n", "    \"\"\"Save final artifacts to the specified directory.\"\"\"\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    train_df.to_csv(os.path.join(output_dir, \"train_data.csv\"), index=False)\n", "    test_df.to_csv(os.path.join(output_dir, \"test_data.csv\"), index=False)\n", "    joblib.dump(scaler, os.path.join(output_dir, \"scaler.pkl\"))\n", "    with open(os.path.join(output_dir, \"class_weights.json\"), \"w\") as f:\n", "        json.dump(class_weights, f)\n", "    print(f\"All artifacts saved to {output_dir}\")\n", "\n", "\n", "### Top Feature Selection and Data Type Fix\n", "\n", "\n", "def select_top_features(df: pd.DataFrame, top_features: list) -> pd.DataFrame:\n", "    \"\"\"\n", "    Keep only the specified top features (plus Label and Threat if present).\n", "    \"\"\"\n", "    keep_cols = top_features.copy()\n", "    if \"Label\" in df.columns:\n", "        keep_cols.append(\"Label\")\n", "    if \"Threat\" in df.columns:\n", "        keep_cols.append(\"Threat\")\n", "    missing = set(keep_cols) - set(df.columns)\n", "    if missing:\n", "        print(f\"Warning: The following top features are missing: {missing}\")\n", "    return df[[col for col in keep_cols if col in df.columns]]\n", "\n", "\n", "def fix_top_features_data_type(df: pd.DataFrame, top_features: list) -> pd.DataFrame:\n", "    \"\"\"\n", "    Convert the top feature columns to their appropriate data types.\n", "    Mapping based on previous processing:\n", "      - Expected as int: 'Fwd Seg Size Min', 'Dst Port', 'TotLen Fwd Pkts',\n", "        'Init Fwd Win Byts', 'Flow Duration', 'Init Bwd Win Byts', 'Flow IAT Max'\n", "      - Expected as float: 'Conn_Rate', 'Bwd Pkt Len Mean', 'Flow IAT Mean'\n", "    \"\"\"\n", "    dtype_mapping = {\n", "        \"Conn_Rate\": float,\n", "        \"Fwd Seg Size Min\": int,\n", "        \"Bwd Pkt Len Mean\": float,\n", "        \"Dst Port\": int,\n", "        \"TotLen Fwd Pkts\": int,\n", "        \"Init Fwd Win Byts\": int,\n", "        \"Flow Duration\": int,\n", "        \"Flow IAT Mean\": float,\n", "        \"Init Bwd Win Byts\": int,\n", "        \"Flow IAT Max\": int,\n", "    }\n", "    for feature in top_features:\n", "        if feature in df.columns:\n", "            try:\n", "                df[feature] = pd.to_numeric(df[feature], errors=\"coerce\").fillna(0)\n", "                df[feature] = df[feature].astype(dtype_mapping[feature])\n", "            except Exception as e:\n", "                print(f\"Error converting {feature}: {e}\")\n", "    return df\n", "\n", "\n", "### Main Preprocessing Pipeline\n", "\n", "\n", "def main(\n", "    base_path: str,\n", "    output_dir: str,\n", "    dates: list,\n", "    chunksize: int = None,\n", "    test_size: float = 0.2,\n", "    random_state: int = 42,\n", "    outlier_strategy: str = \"winsorize\",\n", "    outlier_threshold: float = 3.0,\n", "    use_robust_scaling: bool = True,\n", ") -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame, Dict, Any]:\n", "    \"\"\"\n", "    Main pipeline:\n", "      - Load only necessary columns from each CSV.\n", "      - Clean data and compute the essential DoS feature (Conn_Rate).\n", "      - Drop extra columns early.\n", "      - Select only the top 10 features (plus Label/Threat).\n", "      - Fix data types and prepare data for modeling.\n", "    \"\"\"\n", "    try:\n", "        print(\"\\nStarting data preprocessing pipeline...\")\n", "        dataframes = load_csv_files(base_path, dates, chunksize=chunksize)\n", "        protected_cols = [\"Conn_Rate\", \"Label\", \"Threat\"]\n", "        for date, df in dataframes.items():\n", "            print(f\"\\nProcessing {date}...\")\n", "            df = preprocess_initial_columns(df)\n", "            df = clean_infinite_values(df)\n", "            df[\"Timestamp\"] = pd.to_datetime(df[\"Timestamp\"], errors=\"coerce\")\n", "            df = df.dropna(subset=[\"Timestamp\"])\n", "            print(f\"Computing DoS feature for {date}...\")\n", "            df = add_dos_features(df)\n", "            # Drop Timestamp (and extra computed columns like Concurrency) after computing features\n", "            df = drop_timestamp(df)\n", "            df = generate_binary_label(df)\n", "            df = optimize_memory_usage(df)\n", "            df = transform_multi_label(df)\n", "            print(\"\\nBalancing individual file if needed...\")\n", "            df = balance_dataset_if_needed(df)\n", "            dataframes[date] = df\n", "            print_dataset_info(df, f\"{date} dataset\")\n", "\n", "        print(\"\\nCombining all datasets...\")\n", "        combined_df = pd.concat(dataframes.values(), axis=0, ignore_index=True)\n", "        del dataframes\n", "        print_dataset_info(combined_df, \"Combined dataset\")\n", "\n", "        print(\"\\nHandling outliers...\")\n", "        combined_df = handle_outliers(\n", "            combined_df,\n", "            strategy=outlier_strategy,\n", "            threshold=outlier_threshold,\n", "            exclude_cols=protected_cols,\n", "        )\n", "\n", "        # Define the top 10 features\n", "        top_features = [\n", "            \"Conn_Rate\",\n", "            \"Fwd Seg Size Min\",\n", "            \"Bwd Pkt Len Mean\",\n", "            \"Dst Port\",\n", "            \"TotLen Fwd Pkts\",\n", "            \"Init Fwd Win Byts\",\n", "            \"Flow Duration\",\n", "            \"Flow IAT Mean\",\n", "            \"Init Bwd Win Byts\",\n", "            \"Flow IAT Max\",\n", "        ]\n", "        print(\"\\nSelecting top 10 features...\")\n", "        combined_df = select_top_features(combined_df, top_features)\n", "        print(\"Selected features:\", list(combined_df.columns))\n", "\n", "        # Drop any rows missing the top features\n", "        combined_df.dropna(subset=top_features, inplace=True)\n", "        # Fix data types for these features\n", "        combined_df = fix_top_features_data_type(combined_df, top_features)\n", "\n", "        print(\"\\nRemoving unwanted labels and balancing dataset...\")\n", "        combined_df = remove_labels_and_balance(\n", "            combined_df, [\"Infilteration\", \"Web attack\"], random_state=random_state\n", "        )\n", "\n", "        print(\"\\nPreparing data for modeling...\")\n", "        feature_cols = [\n", "            col for col in combined_df.columns if col not in [\"Label\", \"Threat\"]\n", "        ]\n", "        train_df, test_df = train_test_split(\n", "            combined_df,\n", "            test_size=test_size,\n", "            random_state=random_state,\n", "            shuffle=True,\n", "            stratify=combined_df[\"Label\"],\n", "        )\n", "        train_df, test_df, scaler = apply_robust_scaling(\n", "            train_df, test_df, use_robust=use_robust_scaling\n", "        )\n", "\n", "        unique_labels = np.unique(train_df[\"Label\"])\n", "        class_weights = class_weight.compute_class_weight(\n", "            \"balanced\", classes=unique_labels, y=train_df[\"Label\"].values\n", "        )\n", "        class_weights = dict(zip(unique_labels, class_weights))\n", "        print(\"\\nFinal dataset preparation complete!\")\n", "        print_dataset_info(train_df, \"Training dataset\")\n", "        print_dataset_info(test_df, \"Testing dataset\")\n", "        print(\"\\nClass weights:\", class_weights)\n", "        save_artifacts(train_df, test_df, class_weights, scaler, output_dir)\n", "        print(\"\\nNote: Use StratifiedKFold during modeling for robust validation\")\n", "        return train_df, test_df, class_weights, scaler\n", "    except Exception as e:\n", "        print(f\"\\nError occurred during preprocessing: {e}\")\n", "        raise\n", "\n", "\n", "### Execution Block\n", "\n", "if __name__ == \"__main__\":\n", "    BASE_PATH = r\"C:\\Users\\<USER>\\Desktop\\projects\\big data\\original\"\n", "    OUTPUT_DIR = r\".\\artifacts\"\n", "    DATES = [\n", "        \"02-14-2018\",\n", "        \"02-15-2018\",\n", "        \"02-16-2018\",\n", "        \"02-20-2018\",\n", "        \"02-21-2018\",\n", "        \"02-22-2018\",\n", "        \"02-23-2018\",\n", "        \"02-28-2018\",\n", "        \"03-01-2018\",\n", "        \"03-02-2018\",\n", "    ]\n", "    np.random.seed(42)\n", "    try:\n", "        train_df, test_df, class_weights, scaler = main(\n", "            base_path=BASE_PATH,\n", "            output_dir=OUTPUT_DIR,\n", "            dates=DATES,\n", "            chunksize=100000,\n", "            test_size=0.2,\n", "            random_state=42,\n", "            outlier_strategy=\"winsorize\",\n", "            outlier_threshold=3.0,\n", "            use_robust_scaling=True,\n", "        )\n", "        print(\"\\nPreprocessing pipeline completed successfully!\")\n", "    except Exception as e:\n", "        print(f\"\\nFatal error in preprocessing pipeline: {e}\")\n", "        raise"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}