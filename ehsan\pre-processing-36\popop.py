import pandas as pd
import warnings
import os
import numpy as np
from imblearn.under_sampling import RandomUnderSampler
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.utils import class_weight
import seaborn as sns
import matplotlib.pyplot as plt
import joblib
import json
from typing import Dict, Tuple, Any

# Suppress warnings
warnings.filterwarnings("ignore")

### Data Loading and Initial Preprocessing Functions


def load_csv_files(
    base_path: str, date_list: list, chunksize: int = None
) -> Dict[str, pd.DataFrame]:
    """Load multiple CSV files with optional chunking for large files."""
    dataframes = {}
    for date in date_list:
        file_path = os.path.join(base_path, f"{date}.csv")
        try:
            if chunksize:
                chunks = []
                for chunk in pd.read_csv(
                    file_path, chunksize=chunksize, low_memory=False
                ):
                    chunks.append(chunk)
                dataframes[date] = pd.concat(chunks, ignore_index=True)
            else:
                dataframes[date] = pd.read_csv(file_path, low_memory=False)
            print(
                f"Successfully loaded {date}.csv (Memory: {dataframes[date].memory_usage().sum() / 1024**2:.2f} MB)"
            )
        except Exception as e:
            print(f"Error loading {date}.csv: {str(e)}")
    return dataframes


def preprocess_initial_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Remove specified columns from dataframe."""
    columns_to_drop = ["Flow ID", "Src IP", "Src Port", "Dst IP"]
    df.drop(
        columns=[col for col in columns_to_drop if col in df.columns],
        axis=1,
        inplace=True,
    )
    return df


def fix_data_type(df: pd.DataFrame) -> pd.DataFrame:
    """Fix data types for all columns in the dataframe."""
    df = df[df["Dst Port"] != "Dst Port"]
    type_mapping = {
        "int": [
            "Dst Port",
            "Protocol",
            "Flow Duration",
            "Tot Fwd Pkts",
            "Tot Bwd Pkts",
            "TotLen Fwd Pkts",
            "TotLen Bwd Pkts",
            "Fwd Pkt Len Max",
            "Fwd Pkt Len Min",
            "Bwd Pkt Len Max",
            "Bwd Pkt Len Min",
            "Flow IAT Max",
            "Flow IAT Min",
            "Fwd IAT Tot",
            "Fwd IAT Max",
            "Fwd IAT Min",
            "Bwd IAT Tot",
            "Bwd IAT Max",
            "Bwd IAT Min",
            "Fwd PSH Flags",
            "Bwd PSH Flags",
            "Fwd URG Flags",
            "Bwd URG Flags",
            "Fwd Header Len",
            "Bwd Header Len",
            "Pkt Len Min",
            "Pkt Len Max",
            "FIN Flag Cnt",
            "SYN Flag Cnt",
            "RST Flag Cnt",
            "PSH Flag Cnt",
            "ACK Flag Cnt",
            "URG Flag Cnt",
            "CWE Flag Count",
            "ECE Flag Cnt",
            "Down/Up Ratio",
            "Fwd Byts/b Avg",
            "Fwd Pkts/b Avg",
            "Fwd Blk Rate Avg",
            "Bwd Byts/b Avg",
            "Bwd Pkts/b Avg",
            "Bwd Blk Rate Avg",
            "Subflow Fwd Pkts",
            "Subflow Fwd Byts",
            "Subflow Bwd Pkts",
            "Subflow Bwd Byts",
            "Init Fwd Win Byts",
            "Init Bwd Win Byts",
            "Fwd Act Data Pkts",
            "Fwd Seg Size Min",
            "Active Max",
            "Active Min",
            "Idle Max",
            "Idle Min",
        ],
        "float": [
            "Fwd Pkt Len Mean",
            "Fwd Pkt Len Std",
            "Bwd Pkt Len Mean",
            "Bwd Pkt Len Std",
            "Flow Byts/s",
            "Flow Pkts/s",
            "Flow IAT Mean",
            "Flow IAT Std",
            "Fwd IAT Mean",
            "Fwd IAT Std",
            "Bwd IAT Mean",
            "Bwd IAT Std",
            "Fwd Pkts/s",
            "Bwd Pkts/s",
            "Pkt Len Mean",
            "Pkt Len Std",
            "Pkt Len Var",
            "Pkt Size Avg",
            "Fwd Seg Size Avg",
            "Bwd Seg Size Avg",
            "Active Mean",
            "Active Std",
            "Idle Mean",
            "Idle Std",
        ],
    }
    for col in type_mapping["int"]:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce").fillna(0).astype(int)
    for col in type_mapping["float"]:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce").fillna(0).astype(float)
    return df


def clean_infinite_values(df: pd.DataFrame) -> pd.DataFrame:
    """Replace infinite values with NaN and drop rows with NaN values."""
    df = df.replace(["Infinity", "infinity"], np.inf)
    df = df.replace([np.inf, -np.inf], np.nan)
    initial_rows = len(df)
    df.dropna(inplace=True)
    dropped_rows = initial_rows - len(df)
    if dropped_rows > 0:
        print(f"Dropped {dropped_rows} rows containing NaN values")
    return df


def drop_timestamp(df: pd.DataFrame) -> pd.DataFrame:
    """Remove timestamp column from dataframe."""
    if "Timestamp" in df.columns:
        df.drop(columns="Timestamp", inplace=True)
    return df


### Label Generation and Optimization Functions


def generate_binary_label(df: pd.DataFrame) -> pd.DataFrame:
    """Generate binary classification labels."""
    df["Threat"] = df["Label"].apply(
        lambda x: "Benign" if x == "Benign" else "Malicious"
    )
    return df


def optimize_memory_usage(df: pd.DataFrame) -> pd.DataFrame:
    """Optimize memory usage by adjusting data types."""
    start_mem = df.memory_usage().sum() / 1024**2
    for col in df.columns:
        if df[col].dtype != object:
            if not np.isfinite(df[col]).all():
                df[col] = df[col].fillna(df[col].min() if not df[col].empty else 0)
            col_min, col_max = df[col].min(), df[col].max()
            if np.all(df[col] == df[col].astype(int)):
                if col_min >= 0:
                    if col_max < 255:
                        df[col] = df[col].astype(np.uint8)
                    elif col_max < 65535:
                        df[col] = df[col].astype(np.uint16)
                    elif col_max < 4294967295:
                        df[col] = df[col].astype(np.uint32)
                    else:
                        df[col] = df[col].astype(np.uint64)
                else:
                    if (
                        col_min > np.iinfo(np.int8).min
                        and col_max < np.iinfo(np.int8).max
                    ):
                        df[col] = df[col].astype(np.int8)
                    elif (
                        col_min > np.iinfo(np.int16).min
                        and col_max < np.iinfo(np.int16).max
                    ):
                        df[col] = df[col].astype(np.int16)
                    elif (
                        col_min > np.iinfo(np.int32).min
                        and col_max < np.iinfo(np.int32).max
                    ):
                        df[col] = df[col].astype(np.int32)
                    else:
                        df[col] = df[col].astype(np.int64)
            else:
                df[col] = df[col].astype(np.float32)
    end_mem = df.memory_usage().sum() / 1024**2
    reduction = 100 * (start_mem - end_mem) / start_mem
    print(
        f"Memory usage reduced from {start_mem:.2f} MB to {end_mem:.2f} MB ({reduction:.2f}% reduction)"
    )
    return df


def transform_multi_label(df: pd.DataFrame) -> pd.DataFrame:
    """Transform labels according to attack type mapping."""
    mapping = {
        "SSH-Bruteforce": "Brute-force",
        "FTP-BruteForce": "Brute-force",
        "Brute Force -XSS": "Web attack",
        "Brute Force -Web": "Web attack",
        "SQL Injection": "Web attack",
        "DoS attacks-Hulk": "DoS attack",
        "DoS attacks-SlowHTTPTest": "DoS attack",
        "DoS attacks-Slowloris": "DoS attack",
        "DoS attacks-GoldenEye": "DoS attack",
        "DDOS attack-HOIC": "DDoS attack",
        "DDOS attack-LOIC-UDP": "DDoS attack",
        "DDoS attacks-LOIC-HTTP": "DDoS attack",
        "Bot": "Botnet",
        "Infilteration": "Infilteration",
        "Benign": "Benign",
        "Label": "Benign",
    }
    df["Label"] = df["Label"].map(mapping).fillna("Other")
    print("Label distribution after transformation:")
    print(df["Label"].value_counts())
    return df


### Balancing and Feature Selection Functions


def balance_dataset_if_needed(df: pd.DataFrame) -> pd.DataFrame:
    """Balance the dataset only if benign samples exceed malicious ones."""
    label_counts = df["Label"].value_counts()
    print("Initial class distribution:")
    print(label_counts)
    benign_count = label_counts.get("Benign", 0)
    malicious_count = label_counts.drop("Benign", errors="ignore").sum()
    if benign_count > malicious_count:
        print("Balancing dataset as benign samples exceed malicious ones...")
        X = df.drop(["Label", "Threat"], axis=1)
        y = df["Label"]
        rus = RandomUnderSampler(random_state=42)
        X_balanced, y_balanced = rus.fit_resample(X, y)
        df = pd.concat([X_balanced, pd.Series(y_balanced, name="Label")], axis=1)
        df = generate_binary_label(df)
        print("Class distribution after balancing:")
        print(df["Label"].value_counts())
    else:
        print("No balancing needed as malicious samples exceed or equal benign ones")
    return df


def remove_constant_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Remove columns with zero variance."""
    initial_shape = df.shape
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    variances = df[numeric_cols].var()
    constant_columns = variances[variances == 0].index
    df = df.drop(constant_columns, axis=1)
    print(f"Removed {len(constant_columns)} constant columns")
    print(f"Shape changed from {initial_shape} to {df.shape}")
    return df


def remove_duplicate_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Remove duplicate columns from the dataframe."""
    initial_shape = df.shape
    duplicates = []
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for i in range(len(numeric_cols)):
        for j in range(i + 1, len(numeric_cols)):
            if df[numeric_cols[i]].equals(df[numeric_cols[j]]):
                duplicates.append(numeric_cols[j])
    df = df.drop(duplicates, axis=1)
    print(f"Removed {len(duplicates)} duplicate columns")
    print(f"Shape changed from {initial_shape} to {df.shape}")
    return df


def remove_highly_correlated_features(
    df: pd.DataFrame, threshold: float = 0.90, protected_cols: list = None
) -> pd.DataFrame:
    """Remove highly correlated features, excluding protected columns."""
    if protected_cols is None:
        protected_cols = []
    initial_shape = df.shape
    numeric_cols = [
        col
        for col in df.select_dtypes(include=[np.number]).columns
        if col not in protected_cols
    ]
    if numeric_cols:
        corr = df[numeric_cols].corr()
        upper = corr.where(np.triu(np.ones(corr.shape), k=1).astype(bool))
        to_drop = [
            column for column in upper.columns if any(abs(upper[column]) >= threshold)
        ]
        to_drop = [col for col in to_drop if col not in protected_cols]
        df = df.drop(to_drop, axis=1)
        print(f"Removed {len(to_drop)} highly correlated features")
    else:
        print(
            "No numeric columns to analyze for correlation after excluding protected columns"
        )
    print(f"Shape changed from {initial_shape} to {df.shape}")
    return df


def plot_correlation_heatmap(df: pd.DataFrame, figsize: tuple = (20, 16)) -> None:
    """Plot correlation heatmap with improved visualization."""
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    corr = df[numeric_cols].corr()
    mask = np.triu(np.ones_like(corr, dtype=bool))
    plt.figure(figsize=figsize)
    sns.heatmap(
        corr,
        mask=mask,
        annot=True,
        fmt=".2f",
        cmap="RdBu_r",
        vmin=-1,
        vmax=1,
        square=True,
        linewidths=0.5,
        annot_kws={"size": 8},
    )
    plt.title("Feature Correlation Heatmap")
    plt.tight_layout()
    plt.savefig(".\\correlation_heatmap.png")
    plt.show()


### DoS-Specific and Enhanced Feature Engineering


def add_dos_features(df: pd.DataFrame) -> pd.DataFrame:
    """Add DoS-specific features: Connection Rate and Concurrency."""
    df["Timestamp"] = pd.to_datetime(df["Timestamp"], errors="coerce")
    df = df.dropna(subset=["Timestamp"])
    df = df.reset_index(drop=True)
    df.sort_values(by=["Dst Port", "Timestamp"], inplace=True)
    conn_counts = []
    for name, group in df.groupby("Dst Port"):
        rolling_count = group.rolling("5s", on="Timestamp")["Timestamp"].count()
        conn_counts.append(rolling_count)
    conn_count_series = pd.concat(conn_counts)
    df["Conn_Rate"] = conn_count_series / 5.0
    events = []
    for i, row in df.iterrows():
        start_time = row["Timestamp"]
        duration = row["Flow Duration"]
        end_time = start_time + pd.to_timedelta(duration, unit="us")
        events.append((start_time, "start", row["Dst Port"], i))
        events.append((end_time, "end", row["Dst Port"], i))
    events.sort(key=lambda x: (x[0], x[1]))
    active_flows = {}
    concurrency = pd.Series(0, index=df.index)
    for time, etype, port, idx in events:
        if etype == "start":
            concurrency.loc[idx] = active_flows.get(port, 0)
            active_flows[port] = active_flows.get(port, 0) + 1
        else:
            active_flows[port] = active_flows.get(port, 0) - 1
    df["Concurrency"] = concurrency
    return df


def add_enhanced_temporal_features(df: pd.DataFrame) -> pd.DataFrame:
    """Add enhanced temporal features."""
    df["Flow IAT Skew"] = 0.0
    df["Flow IAT Kurtosis"] = 0.0
    df["Burst_Ratio"] = np.where(df["Flow IAT Mean"] < 0.1, 1.0, 0.0)
    df["Active_Periods_Count"] = 1
    df["Idle_Periods_Ratio"] = 1.0
    return df


def add_packet_size_volume_features(df: pd.DataFrame) -> pd.DataFrame:
    """Add packet size and volume metrics."""
    df["Fwd Pkt Len Median"] = (df["Fwd Pkt Len Max"] + df["Fwd Pkt Len Min"]) / 2.0
    df["Bwd Pkt Len Median"] = (df["Bwd Pkt Len Max"] + df["Bwd Pkt Len Min"]) / 2.0
    df["Fwd Pkt Len 25th"] = df["Fwd Pkt Len Min"]
    df["Fwd Pkt Len 75th"] = df["Fwd Pkt Len Max"]
    df["Bwd Pkt Len 25th"] = df["Bwd Pkt Len Min"]
    df["Bwd Pkt Len 75th"] = df["Bwd Pkt Len Max"]
    if "Flow Byts/s" in df.columns and "Flow Duration" in df.columns:
        df["Total Bytes"] = df["Flow Byts/s"] * df["Flow Duration"]
    else:
        df["Total Bytes"] = 0
    return df


### Outlier Handling and Analysis Functions


def handle_outliers(
    df: pd.DataFrame,
    strategy: str = "winsorize",
    threshold: float = 3.0,
    exclude_cols: list = None,
) -> pd.DataFrame:
    """Handle outliers without removing attack data."""
    if exclude_cols is None:
        exclude_cols = ["Label", "Threat"]
    df_result = df.copy()
    numeric_cols = df_result.select_dtypes(include=[np.number]).columns
    cols_to_process = [col for col in numeric_cols if col not in exclude_cols]
    outlier_stats = {}
    print(f"Processing outliers using {strategy} strategy...")
    attack_dist_before = df_result[df_result["Threat"] == "Malicious"][
        "Label"
    ].value_counts()
    for col in cols_to_process:
        z_scores = np.abs(
            (df_result[col] - df_result[col].mean()) / df_result[col].std()
        )
        outliers = z_scores > threshold
        outlier_count = outliers.sum()
        outlier_stats[col] = outlier_count
        if outlier_count == 0:
            continue
        if strategy == "winsorize":
            lower_bound = df_result[col].quantile(0.01)
            upper_bound = df_result[col].quantile(0.99)
            df_result.loc[df_result[col] < lower_bound, col] = lower_bound
            df_result.loc[df_result[col] > upper_bound, col] = upper_bound
        elif strategy == "cap":
            lower_bound = df_result[col].mean() - (threshold * df_result[col].std())
            upper_bound = df_result[col].mean() + (threshold * df_result[col].std())
            df_result.loc[df_result[col] < lower_bound, col] = lower_bound
            df_result.loc[df_result[col] > upper_bound, col] = upper_bound
        elif strategy == "log_transform":
            if df_result[col].min() >= 0:
                df_result.loc[outliers, col] = np.log1p(df_result.loc[outliers, col])
    total_outliers = sum(outlier_stats.values())
    print(f"Total outliers found across all columns: {total_outliers}")
    if outlier_stats:
        sorted_outliers = {
            k: v
            for k, v in sorted(
                outlier_stats.items(), key=lambda item: item[1], reverse=True
            )
        }
        print("Top 10 columns with most outliers:")
        for i, (col, count) in enumerate(sorted_outliers.items()):
            if i < 10:
                print(f"  {col}: {count} outliers")
    attack_dist_after = df_result[df_result["Threat"] == "Malicious"][
        "Label"
    ].value_counts()
    print("\nAttack distribution before outlier handling:")
    print(attack_dist_before)
    print("\nAttack distribution after outlier handling:")
    print(attack_dist_after)
    return df_result


def analyze_feature_distributions(
    df: pd.DataFrame, sample_size: int = 10000, figsize: tuple = (20, 15)
) -> None:
    """Analyze and visualize feature distributions to identify outliers."""
    df_sample = df.sample(sample_size, random_state=42) if len(df) > sample_size else df
    numeric_cols = df_sample.select_dtypes(include=[np.number]).columns
    n_cols = 3
    n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
    plt.figure(figsize=figsize)
    for i, col in enumerate(numeric_cols):
        plt.subplot(n_rows, n_cols, i + 1)
        sns.boxplot(x="Threat", y=col, data=df_sample)
        plt.title(f"Distribution of {col}")
        plt.xticks(rotation=45)
        plt.tight_layout()
    plt.savefig(".\\feature_distributions.png")
    plt.show()
    outlier_df = pd.DataFrame(index=df.index)
    for col in numeric_cols:
        z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
        outlier_df[f"{col}_outlier"] = (z_scores > 3.0).astype(int)
    pivot = pd.pivot_table(
        pd.concat([outlier_df, df[["Label"]]], axis=1),
        index="Label",
        values=[col for col in outlier_df.columns],
        aggfunc="mean",
    )
    plt.figure(figsize=(15, 10))
    sns.heatmap(pivot, annot=True, fmt=".2f", cmap="YlGnBu")
    plt.title("Percentage of Outliers by Attack Type")
    plt.tight_layout()
    plt.savefig(".\\outlier_attack_correlation.png")
    plt.show()


def detect_anomalies_by_attack_type(
    df: pd.DataFrame, threshold: float = 3.0
) -> pd.DataFrame:
    """Detect which attack types contain the most anomalies in each feature and save results."""
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    attack_outlier_map = {}
    for attack_type in df["Label"].unique():
        attack_df = df[df["Label"] == attack_type]
        total_samples = len(attack_df)
        outlier_counts = {}
        for col in numeric_cols:
            z_scores = np.abs(
                (attack_df[col] - attack_df[col].mean()) / attack_df[col].std()
            )
            outlier_counts[col] = (z_scores > threshold).sum() / total_samples * 100
        attack_outlier_map[attack_type] = outlier_counts
    attack_outlier_df = pd.DataFrame(attack_outlier_map)
    attack_outlier_df.to_csv(os.path.join(OUTPUT_DIR, "outlier_stats_by_attack.csv"))
    print("Top 3 features with most outliers for each attack type:")
    for attack in attack_outlier_df.columns:
        top_features = attack_outlier_df[attack].nlargest(3)
        print(f"\n{attack}:")
        for feature, percentage in top_features.items():
            print(f"  {feature}: {percentage:.2f}% outliers")
    return attack_outlier_df


### Scaling Functions


def scale_features_by_attack_type(
    train_df: pd.DataFrame, test_df: pd.DataFrame
) -> Tuple[pd.DataFrame, pd.DataFrame, Dict]:
    """Apply differential scaling based on attack type (MinMaxScaler per attack type)."""
    feature_cols = [col for col in train_df.columns if col not in ["Label", "Threat"]]
    attack_types = train_df["Label"].unique()
    scalers = {}
    train_scaled = pd.DataFrame()
    test_scaled = pd.DataFrame()
    for attack in attack_types:
        print(f"Scaling features for {attack}...")
        attack_train = train_df[train_df["Label"] == attack]
        scaler = MinMaxScaler()
        scaler.fit(attack_train[feature_cols])
        scalers[attack] = scaler
        attack_train_scaled = attack_train.copy()
        attack_train_scaled[feature_cols] = scaler.transform(attack_train[feature_cols])
        train_scaled = pd.concat([train_scaled, attack_train_scaled])
        attack_test = test_df[test_df["Label"] == attack]
        attack_test_scaled = attack_test.copy()
        attack_test_scaled[feature_cols] = scaler.transform(attack_test[feature_cols])
        test_scaled = pd.concat([test_scaled, attack_test_scaled])
    return train_scaled, test_scaled, scalers


def apply_robust_scaling(
    train_df: pd.DataFrame, test_df: pd.DataFrame, use_robust: bool = True
) -> Tuple[pd.DataFrame, pd.DataFrame, object]:
    """Apply robust scaling using median and IQR or standard MinMaxScaler."""
    feature_cols = [col for col in train_df.columns if col not in ["Label", "Threat"]]
    train_scaled = train_df.copy()
    test_scaled = test_df.copy()
    if use_robust:
        print("Using RobustScaler to handle outliers during scaling...")
        scaler = RobustScaler()
    else:
        print("Using MinMaxScaler for scaling...")
        scaler = MinMaxScaler()
    train_scaled[feature_cols] = scaler.fit_transform(train_df[feature_cols])
    test_scaled[feature_cols] = scaler.transform(test_df[feature_cols])
    return train_scaled, test_scaled, scaler


### Final Data Preparation Functions


def remove_labels_and_balance(
    df: pd.DataFrame, labels_to_remove: list, random_state: int = 42
) -> pd.DataFrame:
    """Remove specified labels and balance the dataset."""
    initial_count = len(df)
    df = df[~df["Label"].isin(labels_to_remove)]
    removed = initial_count - len(df)
    print(f"Removed {removed} rows with labels: {labels_to_remove}")
    min_count = df["Label"].value_counts().min()
    df_balanced = (
        df.groupby("Label")
        .apply(lambda x: x.sample(min_count, random_state=random_state))
        .reset_index(drop=True)
    )
    print("Class distribution after balancing:")
    print(df_balanced["Label"].value_counts())
    return df_balanced


def print_dataset_info(df: pd.DataFrame, name: str = "Dataset") -> None:
    """Print information about the dataset."""
    print(f"\n{name} Information:")
    print(f"Shape: {df.shape}")
    print("\nLabel distribution:")
    print(df["Label"].value_counts())
    print("\nThreat distribution:")
    print(df["Threat"].value_counts())
    print(f"\nNumber of features: {len(df.columns) - 2}")
    print("\nMemory usage:", df.memory_usage().sum() / 1024**2, "MB")


def save_artifacts(
    train_df: pd.DataFrame,
    test_df: pd.DataFrame,
    class_weights: Dict,
    scaler: Any,
    output_dir: str,
) -> None:
    """Save all artifacts to specified directory."""
    os.makedirs(output_dir, exist_ok=True)
    train_df.to_csv(os.path.join(output_dir, "train_data.csv"), index=False)
    test_df.to_csv(os.path.join(output_dir, "test_data.csv"), index=False)
    joblib.dump(scaler, os.path.join(output_dir, "scaler.pkl"))
    with open(os.path.join(output_dir, "class_weights.json"), "w") as f:
        json.dump(class_weights, f)
    print(f"All artifacts saved to {output_dir}")


### Main Preprocessing Pipeline


def main(
    base_path: str,
    output_dir: str,
    dates: list,
    chunksize: int = None,
    correlation_threshold: float = 0.90,
    test_size: float = 0.2,
    random_state: int = 42,
    outlier_strategy: str = "winsorize",
    outlier_threshold: float = 3.0,
    use_robust_scaling: bool = True,
    use_differential_scaling: bool = False,
) -> Tuple[pd.DataFrame, pd.DataFrame, Dict, Any]:
    """Main preprocessing pipeline with outlier handling, scaling options, and chunking for scalability."""
    try:
        print("\nStarting data preprocessing pipeline...")
        dataframes = load_csv_files(base_path, dates, chunksize=chunksize)
        protected_cols = ["Conn_Rate", "Concurrency", "Label", "Threat"]
        for date, df in dataframes.items():
            print(f"\nProcessing {date}...")
            df = preprocess_initial_columns(df)
            df = fix_data_type(df)
            df = clean_infinite_values(df)
            df["Timestamp"] = pd.to_datetime(df["Timestamp"], errors="coerce")
            df = df.dropna(subset=["Timestamp"])
            print(f"Adding DoS features for {date}...")
            df = add_dos_features(df)
            df = add_enhanced_temporal_features(df)
            df = add_packet_size_volume_features(df)
            df = drop_timestamp(df)
            df = generate_binary_label(df)
            df = optimize_memory_usage(df)
            df = transform_multi_label(df)
            print("\nChecking if balancing is needed for individual file...")
            df = balance_dataset_if_needed(df)
            dataframes[date] = df
            print_dataset_info(df, f"{date} dataset")
        print("\nCombining all dataframes...")
        combined_df = pd.concat(dataframes.values(), axis=0, ignore_index=True)
        del dataframes
        print_dataset_info(combined_df, "Combined dataset")
        print("\nAnalyzing feature distributions and outliers...")
        analyze_feature_distributions(combined_df)
        detect_anomalies_by_attack_type(combined_df, threshold=outlier_threshold)
        print("\nHandling outliers without removing attack data...")
        combined_df = handle_outliers(
            combined_df,
            strategy=outlier_strategy,
            threshold=outlier_threshold,
            exclude_cols=protected_cols,
        )
        print("\nPerforming feature selection...")
        combined_df = remove_constant_columns(combined_df)
        combined_df = remove_duplicate_columns(combined_df)
        print("\nGenerating initial correlation heatmap...")
        plot_correlation_heatmap(combined_df)
        combined_df = remove_highly_correlated_features(
            combined_df, correlation_threshold, protected_cols=protected_cols
        )
        print("\nGenerating final correlation heatmap...")
        plot_correlation_heatmap(combined_df)
        print(
            "\nRemoving 'Infilteration' and 'Web attack' labels and balancing dataset..."
        )
        combined_df = remove_labels_and_balance(
            combined_df, ["Infilteration", "Web attack"], random_state=random_state
        )
        print("\nPreparing data for modeling...")
        feature_cols = [
            col for col in combined_df.columns if col not in ["Label", "Threat"]
        ]
        train_df, test_df = train_test_split(
            combined_df,
            test_size=test_size,
            random_state=random_state,
            shuffle=True,
            stratify=combined_df["Label"],
        )
        if use_differential_scaling:
            train_df, test_df, scaler = scale_features_by_attack_type(train_df, test_df)
        else:
            train_df, test_df, scaler = apply_robust_scaling(
                train_df, test_df, use_robust=use_robust_scaling
            )
        unique_labels = np.unique(train_df["Label"])
        class_weights = class_weight.compute_class_weight(
            "balanced", classes=unique_labels, y=train_df["Label"].values
        )
        class_weights = dict(zip(unique_labels, class_weights))
        print("\nFinal dataset preparation complete!")
        print_dataset_info(train_df, "Training dataset")
        print_dataset_info(test_df, "Testing dataset")
        print("\nClass weights:", class_weights)
        save_artifacts(train_df, test_df, class_weights, scaler, output_dir)
        print("\nNote: Use StratifiedKFold during modeling for robust validation")
        return train_df, test_df, class_weights, scaler
    except Exception as e:
        print(f"\nError occurred during preprocessing: {str(e)}")
        raise


### Execution Block

if __name__ == "__main__":
    BASE_PATH = r"C:\Users\<USER>\Desktop\projects\big data\original"
    OUTPUT_DIR = r".\artifacts"
    DATES = [
        "02-14-2018",
        "02-15-2018",
        "02-16-2018",
        "02-20-2018",
        "02-21-2018",
        "02-22-2018",
        "02-23-2018",
        "02-28-2018",
        "03-01-2018",
        "03-02-2018",
    ]
    np.random.seed(42)
    try:
        train_df, test_df, class_weights, scaler = main(
            base_path=BASE_PATH,
            output_dir=OUTPUT_DIR,
            dates=DATES,
            chunksize=100000,
            correlation_threshold=0.90,
            test_size=0.2,
            random_state=42,
            outlier_strategy="winsorize",
            outlier_threshold=3.0,
            use_robust_scaling=True,
            use_differential_scaling=False,
        )
        print("\nPreprocessing pipeline completed successfully!")
    except Exception as e:
        print(f"\nFatal error in preprocessing pipeline: {str(e)}")
        raise