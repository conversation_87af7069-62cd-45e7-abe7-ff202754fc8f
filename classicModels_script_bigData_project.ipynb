{"cells": [{"cell_type": "code", "execution_count": 3, "id": "7f67ba5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading training data from train_data.csv...\n", "Training dataset shape: (1144764, 12)\n", "Loading testing data from test_data.csv...\n", "Testing dataset shape: (286191, 12)\n", "Dropped 'Threat' column from training data\n", "Dropped 'Threat' column from testing data\n", "Training class distribution:\n", "Label\n", "4    228953\n", "0    228953\n", "1    228953\n", "2    228953\n", "3    228952\n", "Name: count, dtype: int64\n", "Testing class distribution:\n", "Label\n", "3    57239\n", "0    57238\n", "2    57238\n", "4    57238\n", "1    57238\n", "Name: count, dtype: int64\n", "Classes: ['Benign' 'Botnet' 'Brute-force' 'DDoS attack' 'DoS attack']\n", "Training set: (1144764, 10), Test set: (286191, 10)\n", "\n", "==================================================\n", "Evaluating AdaBoost...\n", "==================================================\n", "Saved trained model to adab<PERSON>t\\model.joblib\n", "Saved metrics to adaboost\\metrics.json\n", "Accuracy: 0.9026\n", "Precision (macro): 0.9026\n", "Recall (macro): 0.9026\n", "F1 Score (macro): 0.9025\n", "R² Score: 0.0177\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0049\n", "  Botnet: 0.0003\n", "  Brute-force: 0.0535\n", "  DDoS attack: 0.0014\n", "  DoS attack: 0.0616\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       0.98      0.99      0.99     57238\n", "      Botnet       1.00      0.98      0.99     57238\n", " Brute-force       0.78      0.75      0.77     57238\n", " DDoS attack       0.99      1.00      1.00     57239\n", "  DoS attack       0.76      0.78      0.77     57238\n", "\n", "    accuracy                           0.90    286191\n", "   macro avg       0.90      0.90      0.90    286191\n", "weighted avg       0.90      0.90      0.90    286191\n", "\n", "\n", "==================================================\n", "Evaluating Decision Tree...\n", "==================================================\n", "Saved trained model to decision_tree\\model.joblib\n", "Saved metrics to decision_tree\\metrics.json\n", "Accuracy: 0.9870\n", "Precision (macro): 0.9874\n", "Recall (macro): 0.9870\n", "F1 Score (macro): 0.9870\n", "R² Score: 0.9763\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0001\n", "  Botnet: 0.0001\n", "  Brute-force: 0.0138\n", "  DDoS attack: 0.0000\n", "  DoS attack: 0.0023\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.95      0.99      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.99      0.94      0.97     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "==================================================\n", "Evaluating Extra Trees...\n", "==================================================\n", "Saved trained model to extra_trees\\model.joblib\n", "Saved metrics to extra_trees\\metrics.json\n", "Accuracy: 0.9863\n", "Precision (macro): 0.9871\n", "Recall (macro): 0.9863\n", "F1 Score (macro): 0.9863\n", "R² Score: 0.9611\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0002\n", "  Botnet: 0.0001\n", "  Brute-force: 0.0168\n", "  DDoS attack: 0.0001\n", "  DoS attack: 0.0000\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.94      1.00      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       1.00      0.93      0.97     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "==================================================\n", "Evaluating Gradient Boosting...\n", "==================================================\n", "Saved trained model to gradient_boosting\\model.joblib\n", "Saved metrics to gradient_boosting\\metrics.json\n", "Accuracy: 0.9872\n", "Precision (macro): 0.9875\n", "Recall (macro): 0.9872\n", "F1 Score (macro): 0.9872\n", "R² Score: 0.9765\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0000\n", "  Botnet: 0.0000\n", "  Brute-force: 0.0136\n", "  DDoS attack: 0.0000\n", "  DoS attack: 0.0024\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.95      0.99      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.99      0.95      0.97     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "==================================================\n", "Evaluating KNN...\n", "==================================================\n", "Saved trained model to knn\\model.joblib\n", "Saved metrics to knn\\metrics.json\n", "Accuracy: 0.9858\n", "Precision (macro): 0.9860\n", "Recall (macro): 0.9858\n", "F1 Score (macro): 0.9858\n", "R² Score: 0.9714\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0001\n", "  Botnet: 0.0001\n", "  Brute-force: 0.0126\n", "  DDoS attack: 0.0004\n", "  DoS attack: 0.0045\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.95      0.98      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.98      0.95      0.96     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "==================================================\n", "Evaluating LightGBM...\n", "==================================================\n", "[LightGBM] [Warning] Found whitespace in feature_names, replace with underlines\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.004017 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 2214\n", "[LightGBM] [Info] Number of data points in the train set: 1144764, number of used features: 10\n", "[LightGBM] [Info] Start training from score -1.609437\n", "[LightGBM] [Info] Start training from score -1.609437\n", "[LightGBM] [Info] Start training from score -1.609437\n", "[LightGBM] [Info] Start training from score -1.609441\n", "[LightGBM] [Info] Start training from score -1.609437\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "Saved trained model to lightgbm\\model.joblib\n", "Saved metrics to lightgbm\\metrics.json\n", "Accuracy: 0.9868\n", "Precision (macro): 0.9872\n", "Recall (macro): 0.9868\n", "F1 Score (macro): 0.9867\n", "R² Score: 0.9761\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0000\n", "  Botnet: 0.0000\n", "  Brute-force: 0.0142\n", "  DDoS attack: 0.0000\n", "  DoS attack: 0.0023\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.95      0.99      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.99      0.94      0.97     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "==================================================\n", "Evaluating Logistic Regression...\n", "==================================================\n", "Saved trained model to logistic_regression\\model.joblib\n", "Saved metrics to logistic_regression\\metrics.json\n", "Accuracy: 0.5005\n", "Precision (macro): 0.5626\n", "Recall (macro): 0.5005\n", "F1 Score (macro): 0.5108\n", "R² Score: 0.0059\n", "Class-wise False Positive Rates:\n", "  Benign: 0.2218\n", "  Botnet: 0.0432\n", "  Brute-force: 0.0548\n", "  DDoS attack: 0.2489\n", "  DoS attack: 0.0556\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       0.33      0.43      0.37     57238\n", "      Botnet       0.74      0.50      0.60     57238\n", " Brute-force       0.70      0.51      0.59     57238\n", " DDoS attack       0.40      0.65      0.49     57239\n", "  DoS attack       0.65      0.41      0.50     57238\n", "\n", "    accuracy                           0.50    286191\n", "   macro avg       0.56      0.50      0.51    286191\n", "weighted avg       0.56      0.50      0.51    286191\n", "\n", "\n", "==================================================\n", "Evaluating Naive Bayes...\n", "==================================================\n", "Saved trained model to naive_bayes\\model.joblib\n", "Saved metrics to naive_bayes\\metrics.json\n", "Accuracy: 0.4723\n", "Precision (macro): 0.5970\n", "Recall (macro): 0.4724\n", "F1 Score (macro): 0.4272\n", "R² Score: -0.2690\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0121\n", "  Botnet: 0.0834\n", "  Brute-force: 0.5142\n", "  DDoS attack: 0.0033\n", "  DoS attack: 0.0466\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       0.91      0.48      0.63     57238\n", "      Botnet       0.04      0.02      0.02     57238\n", " Brute-force       0.33      1.00      0.49     57238\n", " DDoS attack       0.91      0.13      0.23     57239\n", "  DoS attack       0.80      0.74      0.77     57238\n", "\n", "    accuracy                           0.47    286191\n", "   macro avg       0.60      0.47      0.43    286191\n", "weighted avg       0.60      0.47      0.43    286191\n", "\n", "\n", "==================================================\n", "Evaluating Random Forest...\n", "==================================================\n", "Saved trained model to random_forest\\model.joblib\n", "Saved metrics to random_forest\\metrics.json\n", "Accuracy: 0.9872\n", "Precision (macro): 0.9877\n", "Recall (macro): 0.9872\n", "F1 Score (macro): 0.9872\n", "R² Score: 0.9765\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0000\n", "  Botnet: 0.0000\n", "  Brute-force: 0.0145\n", "  DDoS attack: 0.0000\n", "  DoS attack: 0.0015\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.94      0.99      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.99      0.94      0.97     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "==================================================\n", "Evaluating SGDClassifier...\n", "==================================================\n", "Saved trained model to sgdclassifier\\model.joblib\n", "Saved metrics to sgdclassifier\\metrics.json\n", "Accuracy: 0.8264\n", "Precision (macro): 0.8347\n", "Recall (macro): 0.8264\n", "F1 Score (macro): 0.8279\n", "R² Score: -115666474262.5682\n", "Class-wise False Positive Rates:\n", "  Benign: 0.1003\n", "  Botnet: 0.0292\n", "  Brute-force: 0.0368\n", "  DDoS attack: 0.0476\n", "  DoS attack: 0.0031\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       0.64      0.71      0.67     57238\n", "      Botnet       0.89      0.95      0.92     57238\n", " Brute-force       0.86      0.91      0.89     57238\n", " DDoS attack       0.80      0.75      0.77     57239\n", "  DoS attack       0.99      0.81      0.89     57238\n", "\n", "    accuracy                           0.83    286191\n", "   macro avg       0.83      0.83      0.83    286191\n", "weighted avg       0.83      0.83      0.83    286191\n", "\n", "\n", "==================================================\n", "Evaluating XGBoost...\n", "==================================================\n", "Saved trained model to xgboost\\model.joblib\n", "Saved metrics to xgboost\\metrics.json\n", "Accuracy: 0.9870\n", "Precision (macro): 0.9874\n", "Recall (macro): 0.9870\n", "F1 Score (macro): 0.9870\n", "R² Score: 0.9763\n", "Class-wise False Positive Rates:\n", "  Benign: 0.0000\n", "  Botnet: 0.0000\n", "  Brute-force: 0.0140\n", "  DDoS attack: 0.0000\n", "  DoS attack: 0.0022\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.95      0.99      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.99      0.94      0.97     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n", "\n", "\n", "=== MODEL PERFORMANCE SUMMARY ===\n", "             model_name  accuracy  precision    recall        f1            r2\n", "8         Random Forest  0.987187   0.987692  0.987187  0.987178  9.764689e-01\n", "3     Gradient Boosting  0.987166   0.987545  0.987166  0.987159  9.765112e-01\n", "10              XGBoost  0.987023   0.987441  0.987023  0.987015  9.762826e-01\n", "1         Decision Tree  0.986998   0.987401  0.986998  0.986991  9.762539e-01\n", "5              LightGBM  0.986750   0.987175  0.986750  0.986743  9.761280e-01\n", "2           Extra Trees  0.986296   0.987137  0.986296  0.986279  9.611057e-01\n", "4                   KNN  0.985800   0.986003  0.985800  0.985795  9.714391e-01\n", "0              AdaBoost  0.902562   0.902622  0.902561  0.902496  1.768902e-02\n", "9         SGDClassifier  0.826368   0.834655  0.826368  0.827943 -1.156665e+11\n", "6   Logistic Regression  0.500522   0.562627  0.500522  0.510795  5.946287e-03\n", "7           Naive Bayes  0.472349   0.596953  0.472350  0.427201 -2.689534e-01\n", "\n", "Evaluation complete! All results and visualizations have been saved.\n"]}], "source": ["#10\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import (\n", "    confusion_matrix,\n", "    roc_curve,\n", "    auc,\n", "    classification_report,\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    f1_score,\n", "    r2_score,\n", ")\n", "from sklearn.ensemble import (\n", "    AdaBoostClassifier,\n", "    RandomForestClassifier,\n", "    ExtraTreesClassifier,\n", "    GradientBoostingClassifier,\n", ")\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.linear_model import LogisticRegression, SGDClassifier\n", "from sklearn.naive_bayes import GaussianNB\n", "import xgboost as xgb\n", "import lightgbm as lgb\n", "from sklearn.multiclass import OneVsRestClassifier\n", "from sklearn.preprocessing import label_binarize\n", "import warnings\n", "import os\n", "import json\n", "import joblib\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Set style for plots\n", "plt.style.use(\"seaborn-v0_8-whitegrid\")\n", "plt.rcParams[\"figure.figsize\"] = (12, 10)\n", "plt.rcParams[\"font.size\"] = 12\n", "\n", "\n", "# Define helper functions\n", "def load_and_prepare_data(train_filepath, test_filepath):\n", "    \"\"\"Load data from train and test files, drop Threat column, and prepare X and y.\"\"\"\n", "    from sklearn.preprocessing import LabelEncoder  # Import LabelEncoder\n", "\n", "    print(f\"Loading training data from {train_filepath}...\")\n", "    try:\n", "        train_df = pd.read_csv(train_filepath)\n", "        print(f\"Training dataset shape: {train_df.shape}\")\n", "\n", "        print(f\"Loading testing data from {test_filepath}...\")\n", "        test_df = pd.read_csv(test_filepath)\n", "        print(f\"Testing dataset shape: {test_df.shape}\")\n", "\n", "        # Check if Threat column exists and remove it\n", "        if \"Threat\" in train_df.columns:\n", "            train_df = train_df.drop(\"Threat\", axis=1)\n", "            print(\"Dropped 'Threat' column from training data\")\n", "\n", "        if \"Threat\" in test_df.columns:\n", "            test_df = test_df.drop(\"Threat\", axis=1)\n", "            print(\"Dropped 'Threat' column from testing data\")\n", "\n", "        # Initialize LabelEncoder\n", "        le = LabelEncoder()\n", "\n", "        # Fit LabelEncoder on all unique labels from both train and test\n", "        all_labels = pd.concat([train_df[\"Label\"], test_df[\"Label\"]]).unique()\n", "        le.fit(all_labels)\n", "\n", "        # Store original labels for visualizations\n", "        original_class_names = le.classes_\n", "\n", "        # Transform labels to integers\n", "        train_df[\"Label\"] = le.transform(train_df[\"Label\"])\n", "        test_df[\"Label\"] = le.transform(test_df[\"Label\"])\n", "\n", "        # Extract features and target for training data\n", "        X_train = train_df.drop(\"Label\", axis=1)\n", "        y_train = train_df[\"Label\"]\n", "\n", "        # Extract features and target for testing data\n", "        X_test = test_df.drop(\"Label\", axis=1)\n", "        y_test = test_df[\"Label\"]\n", "\n", "        # Check class distribution\n", "        print(\"Training class distribution:\")\n", "        print(y_train.value_counts())\n", "\n", "        print(\"Testing class distribution:\")\n", "        print(y_test.value_counts())\n", "\n", "        return (\n", "            X_train,\n", "            X_test,\n", "            y_train,\n", "            y_test,\n", "            original_class_names,\n", "        )  # Return original class names\n", "    except Exception as e:\n", "        print(f\"Error loading data: {e}\")\n", "        raise\n", "\n", "\n", "def calculate_fpr(y_true, y_pred):\n", "    \"\"\"Calculate False Positive Rate.\"\"\"\n", "    from sklearn.metrics import confusion_matrix\n", "\n", "    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()\n", "    return fp / (fp + tn)\n", "\n", "\n", "def plot_confusion_matrix(y_true, y_pred, class_names, model_name):\n", "    \"\"\"Plot a styled confusion matrix and save in model-specific folder.\"\"\"\n", "    # Create model-specific folder\n", "    model_folder = model_name.replace(\" \", \"_\").lower()\n", "    os.makedirs(model_folder, exist_ok=True)\n", "\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(\n", "        cm,\n", "        annot=True,\n", "        fmt=\"d\",\n", "        cmap=\"Blues\",\n", "        xticklabels=class_names,\n", "        yticklabels=class_names,\n", "    )\n", "    plt.xlabel(\"Predicted Label\")\n", "    plt.ylabel(\"True Label\")\n", "    plt.title(f\"Confusion Matrix - {model_name}\")\n", "    plt.tight_layout()\n", "    # Save in model-specific folder\n", "    plt.savefig(\n", "        os.path.join(model_folder, \"confusion_matrix.png\"), dpi=300, bbox_inches=\"tight\"\n", "    )\n", "    plt.close()\n", "\n", "\n", "def plot_roc_curves(y_test_bin, y_scores, class_names, model_name):\n", "    \"\"\"Plot ROC curves for all classes and save in model-specific folder.\"\"\"\n", "    # Create model-specific folder\n", "    model_folder = model_name.replace(\" \", \"_\").lower()\n", "    os.makedirs(model_folder, exist_ok=True)\n", "\n", "    plt.figure(figsize=(10, 8))\n", "\n", "    # Calculate ROC curve and ROC area for each class\n", "    fpr = dict()\n", "    tpr = dict()\n", "    roc_auc = dict()\n", "\n", "    n_classes = len(class_names)\n", "\n", "    # Plot ROC for each class\n", "    for i in range(n_classes):\n", "        fpr[i], tpr[i], _ = roc_curve(y_test_bin[:, i], y_scores[:, i])\n", "        roc_auc[i] = auc(fpr[i], tpr[i])\n", "        plt.plot(\n", "            fpr[i], tpr[i], lw=2, label=f\"{class_names[i]} (AUC = {roc_auc[i]:.2f})\"\n", "        )\n", "\n", "    # Calculate micro-average ROC curve and ROC area\n", "    fpr[\"micro\"], tpr[\"micro\"], _ = roc_curve(y_test_bin.ravel(), y_scores.ravel())\n", "    roc_auc[\"micro\"] = auc(fpr[\"micro\"], tpr[\"micro\"])\n", "\n", "    # Calculate macro-average ROC curve and ROC area\n", "    all_fpr = np.unique(np.concatenate([fpr[i] for i in range(n_classes)]))\n", "    mean_tpr = np.zeros_like(all_fpr)\n", "    for i in range(n_classes):\n", "        mean_tpr += np.interp(all_fpr, fpr[i], tpr[i])\n", "    mean_tpr /= n_classes\n", "\n", "    fpr[\"macro\"] = all_fpr\n", "    tpr[\"macro\"] = mean_tpr\n", "    roc_auc[\"macro\"] = auc(fpr[\"macro\"], tpr[\"macro\"])\n", "\n", "    # Plot diagonal\n", "    plt.plot([0, 1], [0, 1], \"k--\", lw=2)\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel(\"False Positive Rate\")\n", "    plt.ylabel(\"True Positive Rate\")\n", "    plt.title(f'{model_name} ROC Curves (Macro AUC = {roc_auc[\"macro\"]:.3f})')\n", "    plt.legend(loc=\"lower right\")\n", "    plt.grid(True)\n", "    # Save in model-specific folder\n", "    plt.savefig(\n", "        os.path.join(model_folder, \"roc_curves.png\"), dpi=300, bbox_inches=\"tight\"\n", "    )\n", "    plt.close()\n", "\n", "    return roc_auc\n", "\n", "\n", "def evaluate_model(model, X_train, X_test, y_train, y_test, model_name, class_names):\n", "    \"\"\"Train, evaluate model, generate metrics and visualizations, and save in model-specific folder.\"\"\"\n", "    # Create model-specific folder\n", "    model_folder = model_name.replace(\" \", \"_\").lower()\n", "    os.makedirs(model_folder, exist_ok=True)\n", "\n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"Evaluating {model_name}...\")\n", "    print(f\"{'='*50}\")\n", "\n", "    # Train the model\n", "    model.fit(X_train, y_train)\n", "\n", "    # Save the trained model\n", "    joblib.dump(model, os.path.join(model_folder, \"model.joblib\"))\n", "    print(f\"Saved trained model to {os.path.join(model_folder, 'model.joblib')}\")\n", "\n", "    # Make predictions\n", "    y_pred = model.predict(X_test)\n", "\n", "    # Get probability scores for ROC curve\n", "    try:\n", "        y_scores = model.predict_proba(X_test)\n", "    except AttributeError:\n", "        try:\n", "            # Fall back to decision_function for models that don't support predict_proba\n", "            y_scores = model.decision_function(X_test)\n", "            # Reshape if needed for binary classifier\n", "            if len(y_scores.shape) == 1:\n", "                y_scores = np.column_stack((1 - y_scores, y_scores))\n", "        except AttributeError:\n", "            # If neither is available, create a simple one-hot encoding of predictions\n", "            print(\n", "                f\"Warning: {model_name} doesn't support predict_proba or decision_function\"\n", "            )\n", "            y_scores = np.zeros((len(y_pred), len(class_names)))\n", "            for i, pred in enumerate(y_pred):\n", "                y_scores[i, pred] = 1\n", "\n", "    # Convert target to binary\n", "    y_test_bin = label_binarize(y_test, classes=range(len(class_names)))\n", "\n", "    # Generate confusion matrix\n", "    plot_confusion_matrix(y_test, y_pred, class_names, model_name)\n", "\n", "    # Generate ROC curves\n", "    roc_auc = plot_roc_curves(y_test_bin, y_scores, class_names, model_name)\n", "\n", "    # Calculate metrics\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "\n", "    # Calculate precision, recall, f1 with macro average (for balanced classes)\n", "    precision = precision_score(y_test, y_pred, average=\"macro\")\n", "    recall = recall_score(y_test, y_pred, average=\"macro\")\n", "    f1 = f1_score(y_test, y_pred, average=\"macro\")\n", "\n", "    # Calculate class-wise metrics\n", "    report = classification_report(\n", "        y_test, y_pred, target_names=class_names, output_dict=True\n", "    )\n", "\n", "    # Calculate FPR for each class\n", "    class_fprs = {}\n", "    for i, class_name in enumerate(class_names):\n", "        # Create binary labels for this class\n", "        binary_y_test = (y_test == i).astype(int)\n", "        binary_y_pred = (y_pred == i).astype(int)\n", "        # Calculate FPR\n", "        tn, fp, fn, tp = confusion_matrix(binary_y_test, binary_y_pred).ravel()\n", "        fpr = fp / (fp + tn) if (fp + tn) > 0 else 0\n", "        class_fprs[class_name] = fpr\n", "\n", "    # Calculate R² (this is approximate for classification)\n", "    # Using <PERSON><PERSON><PERSON><PERSON><PERSON>'s pseudo R² approach\n", "    r2 = r2_score(y_test_bin, y_scores)\n", "\n", "    # Prepare results dictionary\n", "    model_results = {\n", "        \"model_name\": model_name,\n", "        \"accuracy\": accuracy,\n", "        \"precision\": precision,\n", "        \"recall\": recall,\n", "        \"f1\": f1,\n", "        \"r2\": r2,\n", "        \"fpr\": class_fprs,\n", "        \"roc_auc\": roc_auc,\n", "        \"classification_report\": report,\n", "    }\n", "\n", "    # Save detailed metrics to JSON\n", "    with open(os.path.join(model_folder, \"metrics.json\"), \"w\") as f:\n", "        json.dump(model_results, f, indent=4)\n", "    print(f\"Saved metrics to {os.path.join(model_folder, 'metrics.json')}\")\n", "\n", "    # Print metrics\n", "    print(f\"Accuracy: {accuracy:.4f}\")\n", "    print(f\"Precision (macro): {precision:.4f}\")\n", "    print(f\"Recall (macro): {recall:.4f}\")\n", "    print(f\"F1 Score (macro): {f1:.4f}\")\n", "    print(f\"R² Score: {r2:.4f}\")\n", "    print(\"Class-wise False Positive Rates:\")\n", "    for class_name, fpr_value in class_fprs.items():\n", "        print(f\"  {class_name}: {fpr_value:.4f}\")\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(y_test, y_pred, target_names=class_names))\n", "\n", "    return model_results\n", "\n", "\n", "def main():\n", "    \"\"\"Main function to run the entire evaluation process.\"\"\"\n", "    # Define models with enhanced hyperparameters for large datasets (1M+ rows)\n", "    models = {\n", "        \"AdaBoost\": AdaBoostClassifier(\n", "            n_estimators=300,\n", "            learning_rate=1.0,\n", "            algorithm=\"SAMME\",\n", "        ),\n", "        \"Decision Tree\": DecisionTreeClassifier(\n", "            max_depth=15, criterion=\"gini\", splitter=\"best\", min_samples_split=20\n", "        ),\n", "        \"Extra Trees\": ExtraTreesClassifier(\n", "            n_estimators=300,\n", "            max_depth=15,\n", "            min_samples_split=20,\n", "            bootstrap=True,\n", "            n_jobs=-1,\n", "        ),\n", "        \"Gradient Boosting\": GradientBoostingClassifier(\n", "            n_estimators=300,\n", "            learning_rate=0.1,\n", "            max_depth=8,\n", "            subsample=0.8,\n", "            min_samples_split=20,\n", "        ),\n", "        \"KNN\": KNeighborsClassifier(\n", "            n_neighbors=7,\n", "            metric=\"minkowski\",\n", "            weights=\"distance\",\n", "            algorithm=\"ball_tree\",\n", "            n_jobs=-1,\n", "        ),\n", "        \"LightGBM\": lgb.LGBMClassifier(\n", "            n_estimators=400,\n", "            max_depth=10,\n", "            learning_rate=0.1,\n", "            num_leaves=31,\n", "            subsample=0.8,\n", "            colsample_bytree=0.8,\n", "            n_jobs=-1,\n", "        ),\n", "        \"Logistic Regression\": LogisticRegression(\n", "            penalty=\"l2\", C=1.0, solver=\"saga\", max_iter=2000, tol=1e-4, n_jobs=-1\n", "        ),\n", "        \"Naive Bayes\": GaussianNB(var_smoothing=1e-8),\n", "        \"Random Forest\": RandomForestClassifier(\n", "            n_estimators=300,\n", "            max_depth=15,\n", "            min_samples_split=20,\n", "            bootstrap=True,\n", "            n_jobs=-1,\n", "        ),\n", "        \"SGDClassifier\": SGDClassifier(\n", "            loss=\"hinge\", penalty=\"l2\", alpha=0.0001, max_iter=2000, tol=1e-4, n_jobs=-1\n", "        ),\n", "        \"XGBoost\": xgb.XGBClassifier(\n", "            n_estimators=500,\n", "            max_depth=10,\n", "            learning_rate=0.1,\n", "            subsample=0.8,\n", "            colsample_bytree=0.8,\n", "            tree_method=\"hist\",\n", "            nthread=-1,\n", "        ),\n", "    }\n", "\n", "    # File paths to your datasets\n", "    train_file_path = \"train_data.csv\"\n", "    test_file_path = \"test_data.csv\"\n", "\n", "    # Load and prepare data\n", "    try:\n", "        X_train, X_test, y_train, y_test, class_names = load_and_prepare_data(\n", "            train_file_path, test_file_path\n", "        )\n", "\n", "        print(f\"Classes: {class_names}\")\n", "\n", "        print(f\"Training set: {X_train.shape}, Test set: {X_test.shape}\")\n", "\n", "        # Dictionary to store all results\n", "        results = []\n", "\n", "        # Evaluate each model\n", "        for name, model in models.items():\n", "            # For multiclass, wrap certain models in OneVsRestClassifier if needed\n", "            if (\n", "                name in [\"Logistic Regression\", \"SGDClassifier\"]\n", "                and len(class_names) > 2\n", "            ):\n", "                model = OneVsRestClassifier(model)\n", "\n", "            # Evaluate the model\n", "            model_results = evaluate_model(\n", "                model, X_train, X_test, y_train, y_test, name, class_names\n", "            )\n", "            results.append(model_results)\n", "\n", "        # Create a summary DataFrame\n", "        summary = pd.DataFrame(results)\n", "        summary = summary[[\"model_name\", \"accuracy\", \"precision\", \"recall\", \"f1\", \"r2\"]]\n", "\n", "        # Save and display summary in root directory\n", "        print(\"\\n\\n=== MODEL PERFORMANCE SUMMARY ===\")\n", "        print(summary.sort_values(\"f1\", ascending=False))\n", "        summary.to_csv(\"model_performance_summary.csv\", index=False)\n", "\n", "        # Create bar plot for comparison in root directory\n", "        plt.figure(figsize=(14, 10))\n", "        summary_melted = pd.melt(\n", "            summary,\n", "            id_vars=[\"model_name\"],\n", "            value_vars=[\"accuracy\", \"precision\", \"recall\", \"f1\", \"r2\"],\n", "            var_name=\"Metric\",\n", "            value_name=\"Score\",\n", "        )\n", "\n", "        sns.barplot(x=\"model_name\", y=\"Score\", hue=\"Metric\", data=summary_melted)\n", "        plt.xticks(rotation=45, ha=\"right\")\n", "        plt.title(\"Model Performance Comparison\")\n", "        plt.tight_layout()\n", "        plt.savefig(\"model_comparison.png\", dpi=300, bbox_inches=\"tight\")\n", "        plt.close()\n", "\n", "        print(\"\\nEvaluation complete! All results and visualizations have been saved.\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error in main execution: {e}\")\n", "        import traceback\n", "\n", "        traceback.print_exc()\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "Python (torch-gpu)", "language": "python", "name": "torch-gpu"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}