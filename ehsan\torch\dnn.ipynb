{"cells": [{"cell_type": "code", "execution_count": 7, "id": "231e4c51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved class distribution to DNN/images/class_distribution.png\n", "Class weights: [1.00000109 0.99999563 1.00000109 1.00000109 1.00000109]\n", "Epoch 1/50 — Train Loss: 0.1749, Val Loss: 0.1237, Train Acc: 93.02%, Val Acc: 94.50%, LR: 0.00100\n", "Epoch 2/50 — Train Loss: 0.1145, Val Loss: 0.1259, Train Acc: 95.06%, Val Acc: 97.77%, LR: 0.00100\n", "Epoch 3/50 — Train Loss: 0.0924, Val Loss: 0.1083, Train Acc: 96.45%, Val Acc: 96.83%, LR: 0.00100\n", "Epoch 4/50 — Train Loss: 0.0792, Val Loss: 0.2996, Train Acc: 97.17%, Val Acc: 89.19%, LR: 0.00100\n", "Epoch 5/50 — Train Loss: 0.0726, Val Loss: 0.3076, Train Acc: 97.46%, Val Acc: 89.52%, LR: 0.00100\n", "Epoch 6/50 — Train Loss: 0.0689, Val Loss: 0.2531, Train Acc: 97.61%, Val Acc: 89.39%, LR: 0.00100\n", "Epoch 7/50 — Train Loss: 0.0663, Val Loss: 0.3189, Train Acc: 97.72%, Val Acc: 89.45%, LR: 0.00100\n", "Epoch 8/50 — Train Loss: 0.0586, Val Loss: 0.2818, Train Acc: 98.05%, Val Acc: 89.61%, LR: 0.00050\n", "Early stopping at epoch 8\n", "Saved training history to DNN/images/training_history.png\n", "Saved lr schedule to DNN/images/lr_schedule.png\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       0.99      0.99      0.99     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       1.00      0.49      0.66     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       0.66      1.00      0.80     57238\n", "\n", "    accuracy                           0.90    286191\n", "   macro avg       0.93      0.90      0.89    286191\n", "weighted avg       0.93      0.90      0.89    286191\n", "\n", "Saved confusion matrix to DNN/images/confusion_matrix.png\n", "Saved normalized confusion matrix to DNN/images/normalized_confusion_matrix.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saved PR curves to DNN/images/pr_curves.png\n", "Model and weights saved to DNN/\n"]}], "source": ["import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.preprocessing import LabelEncoder, label_binarize\n", "from sklearn.utils.class_weight import compute_class_weight\n", "from sklearn.metrics import (\n", "    classification_report,\n", "    confusion_matrix,\n", "    roc_curve,\n", "    auc,\n", "    precision_recall_curve,\n", "    average_precision_score\n", ")\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Device Configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# Create artifact folders\n", "os.makedirs(\"DNN/images\", exist_ok=True)\n", "\n", "\n", "def load_data(train_path, test_path, test_size=0.2):\n", "    # Load and clean\n", "    train_df = pd.read_csv(train_path)\n", "    test_df  = pd.read_csv(test_path)\n", "    for df in (train_df, test_df):\n", "        if \"Threat\" in df.columns:\n", "            df.drop(columns=[\"Threat\"], inplace=True)\n", "\n", "    # Label encoding\n", "    le_path = \"DNN/label_encoder.pkl\"\n", "    if os.path.exists(le_path):\n", "        label_encoder = joblib.load(le_path)\n", "    else:\n", "        label_encoder = LabelEncoder()\n", "        label_encoder.fit(train_df[\"Label\"])\n", "        joblib.dump(label_encoder, le_path)\n", "\n", "    train_df[\"Label\"] = label_encoder.transform(train_df[\"Label\"])\n", "    test_df [\"Label\"] = label_encoder.transform(test_df [\"Label\"])\n", "\n", "    # Split train/val\n", "    X = train_df.drop(columns=[\"Label\"]).values\n", "    y = train_df[\"Label\"].values\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X, y, test_size=test_size, random_state=42, stratify=y\n", "    )\n", "\n", "    # Test set\n", "    X_test, y_test = (\n", "        test_df.drop(columns=[\"Label\"]).values,\n", "        test_df[\"Label\"].values,\n", "    )\n", "\n", "    return X_train, y_train, X_val, y_val, X_test, y_test, label_encoder\n", "\n", "\n", "class IDS_Network(nn.Module):\n", "    def __init__(self, input_size, num_classes):\n", "        super().__init__()\n", "        self.fc1 = nn.Linear(input_size, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.fc3 = nn.Linear(256, 128)\n", "        self.bn3 = nn.<PERSON>chNorm1d(128)\n", "        self.fc4 = nn.Linear(128, num_classes)\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        x = F.relu(self.bn1(self.fc1(x)))\n", "        x = self.dropout(x)\n", "        x = F.relu(self.bn2(self.fc2(x)))\n", "        x = self.dropout(x)\n", "        x = F.relu(self.bn3(self.fc3(x)))\n", "        x = self.dropout(x)\n", "        return self.fc4(x)\n", "\n", "\n", "def train_model(model, train_loader, val_loader, optimizer,\n", "                criterion, scheduler, epochs, device, patience=5):\n", "    history = {\"train_loss\": [], \"val_loss\": [], \"accuracy\": [], \"lr\": []}\n", "    best_val_loss = float(\"inf\")\n", "    no_improve = 0\n", "\n", "    for epoch in range(1, epochs+1):\n", "        # Record LR\n", "        current_lr = optimizer.param_groups[0]['lr']\n", "        history['lr'].append(current_lr)\n", "\n", "        model.train()\n", "        total_loss = correct = total = 0\n", "        for Xb, yb in train_loader:\n", "            Xb, yb = Xb.to(device), yb.to(device)\n", "            optimizer.zero_grad()\n", "            out = model(Xb)\n", "            loss = criterion(out, yb)\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "            total_loss += loss.item()\n", "            _, preds = out.max(1)\n", "            correct += (preds == yb).sum().item()\n", "            total += yb.size(0)\n", "\n", "        train_loss = total_loss / len(train_loader)\n", "        train_acc  = 100 * correct / total\n", "\n", "        model.eval()\n", "        val_loss = val_correct = val_total = 0\n", "        with torch.no_grad():\n", "            for Xv, yv in val_loader:\n", "                Xv, yv = Xv.to(device), yv.to(device)\n", "                out = model(Xv)\n", "                loss = criterion(out, yv)\n", "                val_loss += loss.item()\n", "                _, preds = out.max(1)\n", "                val_correct += (preds == yv).sum().item()\n", "                val_total += yv.size(0)\n", "\n", "        val_loss /= len(val_loader)\n", "        val_acc  = 100 * val_correct / val_total\n", "\n", "        history[\"train_loss\"].append(train_loss)\n", "        history[\"val_loss\"].append(val_loss)\n", "        history[\"accuracy\"].append(train_acc)\n", "\n", "        print(\n", "            f\"Epoch {epoch}/{epochs} — \"\n", "            f\"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, \"\n", "            f\"Train Acc: {train_acc:.2f}%, Val Acc: {val_acc:.2f}%, LR: {current_lr:.5f}\"\n", "        )\n", "\n", "        if epoch == 11:\n", "            for g in optimizer.param_groups:\n", "                g[\"lr\"] = 5e-4\n", "            print(\"→ LR reduced to 5e-4\")\n", "        elif epoch == 21:\n", "            for g in optimizer.param_groups:\n", "                g[\"lr\"] = 1e-4\n", "            print(\"→ LR reduced to 1e-4\")\n", "\n", "        scheduler.step(val_loss)\n", "\n", "        if val_loss < best_val_loss:\n", "            best_val_loss = val_loss\n", "            no_improve = 0\n", "        else:\n", "            no_improve += 1\n", "            if no_improve >= patience:\n", "                print(f\"Early stopping at epoch {epoch}\")\n", "                break\n", "\n", "    return history\n", "\n", "\n", "def plot_training_history(history):\n", "    epochs = range(1, len(history[\"train_loss\"]) + 1)\n", "    fig, axes = plt.subplots(1, 2, figsize=(12,5))\n", "\n", "    axes[0].plot(epochs, history[\"train_loss\"], label=\"Train Loss\")\n", "    axes[0].plot(epochs, history[\"val_loss\"],   label=\"Val Loss\")\n", "    axes[0].set_xlabel(\"Epoch\")\n", "    axes[0].set_ylabel(\"Loss\")\n", "    axes[0].set_title(\"Loss vs. Epoch\")\n", "    axes[0].legend()\n", "\n", "    axes[1].plot(epochs, history[\"accuracy\"], label=\"Train Accuracy\")\n", "    axes[1].set_xlabel(\"Epoch\")\n", "    axes[1].set_ylabel(\"Accuracy (%)\")\n", "    axes[1].set_title(\"Training Accuracy\")\n", "    axes[1].legend()\n", "\n", "    fig.tight_layout()\n", "    path = \"DNN/images/training_history.png\"\n", "    fig.savefig(path)\n", "    plt.close(fig)\n", "    print(f\"Saved training history to {path}\")\n", "\n", "\n", "def plot_lr_schedule(history):\n", "    epochs = range(1, len(history[\"lr\"]) + 1)\n", "    fig = plt.figure(figsize=(6,4))\n", "    plt.plot(epochs, history[\"lr\"], marker='o')\n", "    plt.xlabel(\"Epoch\")\n", "    plt.ylabel(\"Learning Rate\")\n", "    plt.title(\"Learning Rate Schedule\")\n", "    path = \"DNN/images/lr_schedule.png\"\n", "    fig.savefig(path)\n", "    plt.close(fig)\n", "    print(f\"Saved lr schedule to {path}\")\n", "\n", "\n", "def plot_normalized_confusion(cm, classes):\n", "    cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "    fig = plt.figure(figsize=(8,6))\n", "    sns.heatmap(cm_norm, annot=True, fmt=\".2f\", cmap=\"Blues\",\n", "                xticklabels=classes, yticklabels=classes)\n", "    plt.xlabel(\"Predicted\")\n", "    plt.ylabel(\"Actual\")\n", "    plt.title(\"Normalized Confusion Matrix\")\n", "    path = \"DNN/images/normalized_confusion_matrix.png\"\n", "    fig.savefig(path)\n", "    plt.close(fig)\n", "    print(f\"Saved normalized confusion matrix to {path}\")\n", "\n", "\n", "def plot_pr_curves(y_true, probs, classes):\n", "    n_classes = len(classes)\n", "    y_bin = label_binarize(y_true, classes=range(n_classes))\n", "\n", "    fig = plt.figure(figsize=(8,6))\n", "    for i in range(n_classes):\n", "        precision, recall, _ = precision_recall_curve(y_bin[:, i], probs[:, i])\n", "        ap = average_precision_score(y_bin[:, i], probs[:, i])\n", "        plt.plot(recall, precision, label=f\"{classes[i]} (AP = {ap:.2f})\")\n", "    plt.xlabel(\"Recall\")\n", "    plt.ylabel(\"Precision\")\n", "    plt.title(\"Precision-Recall Curves\")\n", "    plt.legend(loc=\"lower left\")\n", "    path = \"DNN/images/pr_curves.png\"\n", "    fig.savefig(path)\n", "    plt.close(fig)\n", "    print(f\"Saved PR curves to {path}\")\n", "\n", "\n", "def plot_class_distribution(y_train, y_test, classes):\n", "    counts_train = np.bincount(y_train, minlength=len(classes))\n", "    counts_test = np.bincount(y_test,  minlength=len(classes))\n", "    x = np.arange(len(classes))\n", "\n", "    fig = plt.figure(figsize=(10,6))\n", "    plt.bar(x - 0.2, counts_train, width=0.4, label=\"Train\")\n", "    plt.bar(x + 0.2, counts_test,  width=0.4, label=\"Test\")\n", "    plt.xticks(x, classes, rotation=45)\n", "    plt.xlabel(\"Class\")\n", "    plt.ylabel(\"Count\")\n", "    plt.title(\"Class Distribution\")\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    path = \"DNN/images/class_distribution.png\"\n", "    fig.savefig(path)\n", "    plt.close(fig)\n", "    print(f\"Saved class distribution to {path}\")\n", "\n", "\n", "def evaluate_model(model, test_loader, device, label_encoder):\n", "    model.eval()\n", "    y_true, y_pred, prob_list = [], [], []\n", "\n", "    with torch.no_grad():\n", "        for Xb, yb in test_loader:\n", "            Xb, yb = Xb.to(device), yb.to(device)\n", "            out = model(Xb)\n", "            probs = <PERSON>.softmax(out, dim=1)\n", "            preds = probs.argmax(dim=1)\n", "\n", "            y_true.extend(yb.cpu().numpy())\n", "            y_pred.extend(preds.cpu().numpy())\n", "            prob_list.append(probs.cpu().numpy())\n", "\n", "    probs_all = np.vstack(prob_list)\n", "    classes = list(label_encoder.classes_)\n", "\n", "    print(classification_report(y_true, y_pred, target_names=classes))\n", "\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    fig = plt.figure(figsize=(8,6))\n", "    sns.heatmap(cm, annot=True, fmt=\"d\", cmap=\"Blues\",\n", "                xticklabels=classes, yticklabels=classes)\n", "    plt.xlabel(\"Predicted\")\n", "    plt.ylabel(\"Actual\")\n", "    plt.title(\"Confusion Matrix\")\n", "    path_cm = \"DNN/images/confusion_matrix.png\"\n", "    fig.savefig(path_cm)\n", "    plt.close(fig)\n", "    print(f\"Saved confusion matrix to {path_cm}\")\n", "\n", "    plot_normalized_confusion(cm, classes)\n", "    plot_roc(np.array(y_true), probs_all, classes)\n", "    plot_pr_curves(np.array(y_true), probs_all, classes)\n", "\n", "    return y_pred, y_true, classes\n", "\n", "\n", "def main(train_path, test_path, epochs=50,\n", "         batch_size=256, learning_rate=1e-3):\n", "    X_tr, y_tr, X_val, y_val, X_te, y_te, le = load_data(train_path, test_path)\n", "\n", "    # Save class distribution\n", "    plot_class_distribution(y_tr, y_te, list(le.classes_))\n", "\n", "    tX_tr = torch.tensor(X_tr, dtype=torch.float32)\n", "    ty_tr = torch.tensor(y_tr, dtype=torch.long)\n", "    tX_val = torch.tensor(X_val, dtype=torch.float32)\n", "    ty_val = torch.tensor(y_val, dtype=torch.long)\n", "    tX_te = torch.tensor(X_te, dtype=torch.float32)\n", "    ty_te = torch.tensor(y_te, dtype=torch.long)\n", "\n", "    train_dl = DataLoader(TensorDataset(tX_tr, ty_tr), batch_size=batch_size, shuffle=True)\n", "    val_dl   = DataLoader(TensorDataset(tX_val, ty_val), batch_size=batch_size, shuffle=False)\n", "    test_dl  = DataLoader(TensorDataset(tX_te, ty_te), batch_size=batch_size, shuffle=False)\n", "\n", "    model = IDS_Network(X_tr.shape[1], len(le.classes_)).to(device)\n", "    cw = compute_class_weight(class_weight=\"balanced\", classes=np.unique(y_tr), y=y_tr)\n", "    cw_tensor = torch.tensor(cw, dtype=torch.float).to(device)\n", "    print(\"Class weights:\", cw)\n", "\n", "    optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.CrossEntropyLoss(weight=cw_tensor)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode=\"min\", patience=3, factor=0.5)\n", "\n", "    history = train_model(model, train_dl, val_dl, optimizer, criterion, scheduler, epochs, device)\n", "\n", "    plot_training_history(history)\n", "    plot_lr_schedule(history)\n", "\n", "    evaluate_model(model, test_dl, device, le)\n", "\n", "    torch.save(model.state_dict(), \"DNN/model_with_class_weights.pth\")\n", "    np.save(\"DNN/class_weights.npy\", cw)\n", "    print(\"Model and weights saved to DNN/\")\n", "\n", "if __name__ == \"__main__\":\n", "    main(\"train_data.csv\", \"test_data.csv\", epochs=50)"]}, {"cell_type": "code", "execution_count": 2, "id": "2ab3ada6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Early stopping at epoch 41\n", "Saved evaluation metrics to DNN/metrics.txt\n", "Model and weights saved to DNN/\n"]}], "source": ["import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.preprocessing import LabelEncoder, label_binarize\n", "from sklearn.utils.class_weight import compute_class_weight\n", "from sklearn.metrics import (\n", "    classification_report,\n", "    confusion_matrix,\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    f1_score,\n", "    roc_auc_score,\n", "    precision_recall_curve,\n", "    average_precision_score\n", ")\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Device Configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "\n", "def load_data(train_path, test_path, test_size=0.2):\n", "    train_df = pd.read_csv(train_path)\n", "    test_df  = pd.read_csv(test_path)\n", "    for df in (train_df, test_df):\n", "        if \"Threat\" in df.columns:\n", "            df.drop(columns=[\"Threat\"], inplace=True)\n", "\n", "    le_path = \"DNN/label_encoder.pkl\"\n", "    if os.path.exists(le_path):\n", "        label_encoder = joblib.load(le_path)\n", "    else:\n", "        label_encoder = LabelEncoder()\n", "        label_encoder.fit(train_df[\"Label\"])\n", "        joblib.dump(label_encoder, le_path)\n", "\n", "    train_df[\"Label\"] = label_encoder.transform(train_df[\"Label\"])\n", "    test_df [\"Label\"] = label_encoder.transform(test_df [\"Label\"])\n", "\n", "    X = train_df.drop(columns=[\"Label\"]).values\n", "    y = train_df[\"Label\"].values\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X, y, test_size=test_size, random_state=42, stratify=y\n", "    )\n", "\n", "    X_test, y_test = (\n", "        test_df.drop(columns=[\"Label\"]).values,\n", "        test_df[\"Label\"].values,\n", "    )\n", "\n", "    return X_train, y_train, X_val, y_val, X_test, y_test, label_encoder\n", "\n", "\n", "class IDS_Network(nn.Module):\n", "    def __init__(self, input_size, num_classes):\n", "        super().__init__()\n", "        self.fc1 = nn.Linear(input_size, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.fc3 = nn.Linear(256, 128)\n", "        self.bn3 = nn.<PERSON>chNorm1d(128)\n", "        self.fc4 = nn.Linear(128, num_classes)\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        x = F.relu(self.bn1(self.fc1(x)))\n", "        x = self.dropout(x)\n", "        x = F.relu(self.bn2(self.fc2(x)))\n", "        x = self.dropout(x)\n", "        x = F.relu(self.bn3(self.fc3(x)))\n", "        x = self.dropout(x)\n", "        return self.fc4(x)\n", "\n", "\n", "def train_model(model, train_loader, val_loader, optimizer,\n", "                criterion, scheduler, epochs, device, patience=5):\n", "    best_val_loss = float(\"inf\")\n", "    no_improve = 0\n", "\n", "    for epoch in range(1, epochs+1):\n", "        model.train()\n", "        total_loss = correct = total = 0\n", "        for Xb, yb in train_loader:\n", "            Xb, yb = Xb.to(device), yb.to(device)\n", "            optimizer.zero_grad()\n", "            out = model(Xb)\n", "            loss = criterion(out, yb)\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "            total_loss += loss.item()\n", "            _, preds = out.max(1)\n", "            correct += (preds == yb).sum().item()\n", "            total += yb.size(0)\n", "\n", "        model.eval()\n", "        val_loss = val_correct = val_total = 0\n", "        with torch.no_grad():\n", "            for Xv, yv in val_loader:\n", "                Xv, yv = Xv.to(device), yv.to(device)\n", "                out = model(Xv)\n", "                loss = criterion(out, yv)\n", "                val_loss += loss.item()\n", "                _, preds = out.max(1)\n", "                val_correct += (preds == yv).sum().item()\n", "                val_total += yv.size(0)\n", "\n", "        val_loss /= len(val_loader)\n", "        if val_loss < best_val_loss:\n", "            best_val_loss = val_loss\n", "            no_improve = 0\n", "        else:\n", "            no_improve += 1\n", "            if no_improve >= patience:\n", "                print(f\"Early stopping at epoch {epoch}\")\n", "                break\n", "\n", "        scheduler.step(val_loss)\n", "\n", "    return model\n", "\n", "\n", "def evaluate_and_save(model, test_loader, device, label_encoder):\n", "    model.eval()\n", "    y_true, y_pred, probs = [], [], []\n", "\n", "    with torch.no_grad():\n", "        for Xb, yb in test_loader:\n", "            Xb, yb = Xb.to(device), yb.to(device)\n", "            out = model(Xb)\n", "            prob = <PERSON>.softmax(out, dim=1)\n", "            preds = prob.argmax(dim=1)\n", "\n", "            y_true.extend(yb.cpu().numpy())\n", "            y_pred.extend(preds.cpu().numpy())\n", "            probs.append(prob.cpu().numpy())\n", "\n", "    probs = np.vstack(probs)\n", "    classes = list(label_encoder.classes_)\n", "    y_true_arr = np.array(y_true)\n", "    y_pred_arr = np.array(y_pred)\n", "\n", "    # Basic metrics\n", "    acc = accuracy_score(y_true_arr, y_pred_arr)\n", "    prec = precision_score(y_true_arr, y_pred_arr, average='weighted')\n", "    rec = recall_score(y_true_arr, y_pred_arr, average='weighted')\n", "    f1 = f1_score(y_true_arr, y_pred_arr, average='weighted')\n", "\n", "    # ROC AUC\n", "    y_bin = label_binarize(y_true_arr, classes=range(len(classes)))\n", "    roc_auc = roc_auc_score(y_bin, probs, average='macro', multi_class='ovr')\n", "\n", "    # Average precision per class\n", "    ap_scores = {}\n", "    for i, cls in enumerate(classes):\n", "        ap = average_precision_score(y_bin[:, i], probs[:, i])\n", "        ap_scores[cls] = ap\n", "\n", "    # Save to txt\n", "    os.makedirs(\"DNN\", exist_ok=True)\n", "    with open(\"DNN/metrics.txt\", \"w\") as f:\n", "        f.write(f\"Accuracy: {acc:.4f}\\n\")\n", "        f.write(f\"Precision (weighted): {prec:.4f}\\n\")\n", "        f.write(f\"Recall (weighted): {rec:.4f}\\n\")\n", "        f.write(f\"F1 Score (weighted): {f1:.4f}\\n\")\n", "        f.write(f\"ROC AUC (macro, OVR): {roc_auc:.4f}\\n\\n\")\n", "        f.write(\"Average Precision per Class:\\n\")\n", "        for cls, ap in ap_scores.items():\n", "            f.write(f\"  {cls}: {ap:.4f}\\n\")\n", "        f.write(\"\\nClassification Report:\\n\")\n", "        f.write(classification_report(y_true_arr, y_pred_arr, target_names=classes))\n", "        f.write(\"\\nConfusion Matrix:\\n\")\n", "        f.write(np.array2string(confusion_matrix(y_true_arr, y_pred_arr)))\n", "\n", "    print(\"Saved evaluation metrics to DNN/metrics.txt\")\n", "\n", "\n", "def main(train_path, test_path, epochs=50,\n", "         batch_size=256, learning_rate=1e-3):\n", "    X_tr, y_tr, X_val, y_val, X_te, y_te, le = load_data(train_path, test_path)\n", "\n", "    tX_tr = torch.tensor(X_tr, dtype=torch.float32)\n", "    ty_tr = torch.tensor(y_tr, dtype=torch.long)\n", "    tX_val = torch.tensor(X_val, dtype=torch.float32)\n", "    ty_val = torch.tensor(y_val, dtype=torch.long)\n", "    tX_te = torch.tensor(X_te, dtype=torch.float32)\n", "    ty_te = torch.tensor(y_te, dtype=torch.long)\n", "\n", "    train_dl = DataLoader(TensorDataset(tX_tr, ty_tr), batch_size=batch_size, shuffle=True)\n", "    val_dl   = DataLoader(TensorDataset(tX_val, ty_val), batch_size=batch_size, shuffle=False)\n", "    test_dl  = DataLoader(TensorDataset(tX_te, ty_te), batch_size=batch_size, shuffle=False)\n", "\n", "    model = IDS_Network(X_tr.shape[1], len(le.classes_)).to(device)\n", "    cw = compute_class_weight(class_weight=\"balanced\", classes=np.unique(y_tr), y=y_tr)\n", "    cw_tensor = torch.tensor(cw, dtype=torch.float).to(device)\n", "\n", "    optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.CrossEntropyLoss(weight=cw_tensor)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode=\"min\", patience=3, factor=0.5)\n", "\n", "    trained_model = train_model(model, train_dl, val_dl, optimizer, criterion, scheduler, epochs, device)\n", "    evaluate_and_save(trained_model, test_dl, device, le)\n", "\n", "    torch.save(trained_model.state_dict(), \"DNN/model_weights.pth\")\n", "    np.save(\"DNN/class_weights.npy\", cw)\n", "    print(\"Model and weights saved to DNN/\")\n", "\n", "if __name__ == \"__main__\":\n", "    main(\"train_data.csv\", \"test_data.csv\", epochs=50)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "1b3e0fd6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Early stopping at epoch 6\n", "Saved evaluation metrics to DNN/metrics.txt\n", "Model and weights saved to DNN/\n"]}], "source": ["#10 features\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.preprocessing import LabelEncoder, label_binarize\n", "from sklearn.utils.class_weight import compute_class_weight\n", "from sklearn.metrics import (\n", "    classification_report,\n", "    confusion_matrix,\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    f1_score,\n", "    roc_auc_score,\n", "    precision_recall_curve,\n", "    average_precision_score\n", ")\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Device Configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "\n", "def load_data(train_path, test_path, test_size=0.2):\n", "    train_df = pd.read_csv(train_path)\n", "    test_df  = pd.read_csv(test_path)\n", "    for df in (train_df, test_df):\n", "        if \"Threat\" in df.columns:\n", "            df.drop(columns=[\"Threat\"], inplace=True)\n", "\n", "    le_path = \"DNN/label_encoder.pkl\"\n", "    if os.path.exists(le_path):\n", "        label_encoder = joblib.load(le_path)\n", "    else:\n", "        label_encoder = LabelEncoder()\n", "        label_encoder.fit(train_df[\"Label\"])\n", "        joblib.dump(label_encoder, le_path)\n", "\n", "    train_df[\"Label\"] = label_encoder.transform(train_df[\"Label\"])\n", "    test_df [\"Label\"] = label_encoder.transform(test_df [\"Label\"])\n", "\n", "    X = train_df.drop(columns=[\"Label\"]).values\n", "    y = train_df[\"Label\"].values\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X, y, test_size=test_size, random_state=42, stratify=y\n", "    )\n", "\n", "    X_test, y_test = (\n", "        test_df.drop(columns=[\"Label\"]).values,\n", "        test_df[\"Label\"].values,\n", "    )\n", "\n", "    return X_train, y_train, X_val, y_val, X_test, y_test, label_encoder\n", "\n", "\n", "class IDS_Network(nn.Module):\n", "    def __init__(self, input_size, num_classes):\n", "        super().__init__()\n", "        self.fc1 = nn.Linear(input_size, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.fc3 = nn.Linear(256, 128)\n", "        self.bn3 = nn.<PERSON>chNorm1d(128)\n", "        self.fc4 = nn.Linear(128, num_classes)\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        x = F.relu(self.bn1(self.fc1(x)))\n", "        x = self.dropout(x)\n", "        x = F.relu(self.bn2(self.fc2(x)))\n", "        x = self.dropout(x)\n", "        x = F.relu(self.bn3(self.fc3(x)))\n", "        x = self.dropout(x)\n", "        return self.fc4(x)\n", "\n", "\n", "def train_model(model, train_loader, val_loader, optimizer,\n", "                criterion, scheduler, epochs, device, patience=5):\n", "    best_val_loss = float(\"inf\")\n", "    no_improve = 0\n", "\n", "    for epoch in range(1, epochs+1):\n", "        model.train()\n", "        total_loss = correct = total = 0\n", "        for Xb, yb in train_loader:\n", "            Xb, yb = Xb.to(device), yb.to(device)\n", "            optimizer.zero_grad()\n", "            out = model(Xb)\n", "            loss = criterion(out, yb)\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "            total_loss += loss.item()\n", "            _, preds = out.max(1)\n", "            correct += (preds == yb).sum().item()\n", "            total += yb.size(0)\n", "\n", "        model.eval()\n", "        val_loss = val_correct = val_total = 0\n", "        with torch.no_grad():\n", "            for Xv, yv in val_loader:\n", "                Xv, yv = Xv.to(device), yv.to(device)\n", "                out = model(Xv)\n", "                loss = criterion(out, yv)\n", "                val_loss += loss.item()\n", "                _, preds = out.max(1)\n", "                val_correct += (preds == yv).sum().item()\n", "                val_total += yv.size(0)\n", "\n", "        val_loss /= len(val_loader)\n", "        if val_loss < best_val_loss:\n", "            best_val_loss = val_loss\n", "            no_improve = 0\n", "        else:\n", "            no_improve += 1\n", "            if no_improve >= patience:\n", "                print(f\"Early stopping at epoch {epoch}\")\n", "                break\n", "\n", "        scheduler.step(val_loss)\n", "\n", "    return model\n", "\n", "\n", "def evaluate_and_save(model, test_loader, device, label_encoder):\n", "    model.eval()\n", "    y_true, y_pred, probs = [], [], []\n", "\n", "    with torch.no_grad():\n", "        for Xb, yb in test_loader:\n", "            Xb, yb = Xb.to(device), yb.to(device)\n", "            out = model(Xb)\n", "            prob = <PERSON>.softmax(out, dim=1)\n", "            preds = prob.argmax(dim=1)\n", "\n", "            y_true.extend(yb.cpu().numpy())\n", "            y_pred.extend(preds.cpu().numpy())\n", "            probs.append(prob.cpu().numpy())\n", "\n", "    probs = np.vstack(probs)\n", "    classes = list(label_encoder.classes_)\n", "    y_true_arr = np.array(y_true)\n", "    y_pred_arr = np.array(y_pred)\n", "\n", "    # Basic metrics\n", "    acc = accuracy_score(y_true_arr, y_pred_arr)\n", "    prec = precision_score(y_true_arr, y_pred_arr, average='weighted')\n", "    rec = recall_score(y_true_arr, y_pred_arr, average='weighted')\n", "    f1 = f1_score(y_true_arr, y_pred_arr, average='weighted')\n", "\n", "    # ROC AUC\n", "    y_bin = label_binarize(y_true_arr, classes=range(len(classes)))\n", "    roc_auc = roc_auc_score(y_bin, probs, average='macro', multi_class='ovr')\n", "\n", "    # Average precision per class\n", "    ap_scores = {}\n", "    for i, cls in enumerate(classes):\n", "        ap = average_precision_score(y_bin[:, i], probs[:, i])\n", "        ap_scores[cls] = ap\n", "\n", "    # Save to txt\n", "    os.makedirs(\"DNN\", exist_ok=True)\n", "    with open(\"DNN/metrics.txt\", \"w\") as f:\n", "        f.write(f\"Accuracy: {acc:.4f}\\n\")\n", "        f.write(f\"Precision (weighted): {prec:.4f}\\n\")\n", "        f.write(f\"Recall (weighted): {rec:.4f}\\n\")\n", "        f.write(f\"F1 Score (weighted): {f1:.4f}\\n\")\n", "        f.write(f\"ROC AUC (macro, OVR): {roc_auc:.4f}\\n\\n\")\n", "        f.write(\"Average Precision per Class:\\n\")\n", "        for cls, ap in ap_scores.items():\n", "            f.write(f\"  {cls}: {ap:.4f}\\n\")\n", "        f.write(\"\\nClassification Report:\\n\")\n", "        f.write(classification_report(y_true_arr, y_pred_arr, target_names=classes))\n", "        f.write(\"\\nConfusion Matrix:\\n\")\n", "        f.write(np.array2string(confusion_matrix(y_true_arr, y_pred_arr)))\n", "\n", "    print(\"Saved evaluation metrics to DNN/metrics.txt\")\n", "\n", "\n", "def main(train_path, test_path, epochs=50,\n", "         batch_size=256, learning_rate=1e-3):\n", "    X_tr, y_tr, X_val, y_val, X_te, y_te, le = load_data(train_path, test_path)\n", "\n", "    tX_tr = torch.tensor(X_tr, dtype=torch.float32)\n", "    ty_tr = torch.tensor(y_tr, dtype=torch.long)\n", "    tX_val = torch.tensor(X_val, dtype=torch.float32)\n", "    ty_val = torch.tensor(y_val, dtype=torch.long)\n", "    tX_te = torch.tensor(X_te, dtype=torch.float32)\n", "    ty_te = torch.tensor(y_te, dtype=torch.long)\n", "\n", "    train_dl = DataLoader(TensorDataset(tX_tr, ty_tr), batch_size=batch_size, shuffle=True)\n", "    val_dl   = DataLoader(TensorDataset(tX_val, ty_val), batch_size=batch_size, shuffle=False)\n", "    test_dl  = DataLoader(TensorDataset(tX_te, ty_te), batch_size=batch_size, shuffle=False)\n", "\n", "    model = IDS_Network(X_tr.shape[1], len(le.classes_)).to(device)\n", "    cw = compute_class_weight(class_weight=\"balanced\", classes=np.unique(y_tr), y=y_tr)\n", "    cw_tensor = torch.tensor(cw, dtype=torch.float).to(device)\n", "\n", "    optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.CrossEntropyLoss(weight=cw_tensor)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode=\"min\", patience=3, factor=0.5)\n", "\n", "    trained_model = train_model(model, train_dl, val_dl, optimizer, criterion, scheduler, epochs, device)\n", "    evaluate_and_save(trained_model, test_dl, device, le)\n", "\n", "    torch.save(trained_model.state_dict(), \"DNN/model_weights.pth\")\n", "    np.save(\"DNN/class_weights.npy\", cw)\n", "    print(\"Model and weights saved to DNN/\")\n", "\n", "if __name__ == \"__main__\":\n", "    main(\"train_data.csv\", \"test_data.csv\", epochs=50)\n"]}], "metadata": {"kernelspec": {"display_name": "Python (torch-gpu)", "language": "python", "name": "torch-gpu"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}