model_name,accuracy,precision,recall,f1,r2
AdaBoost,0.9025615760104266,0.9026221557913987,0.9025612500105998,0.9024961542348775,0.017689019145086582
<PERSON> Tree,0.9869981935141217,0.9874010523455882,0.9869981482055321,0.9869911830344853,0.9762539109221903
Extra Trees,0.9862958653486658,0.987137465223267,0.9862958177081046,0.9862785382620318,0.9611056601597348
Gradient Boosting,0.9871659136730365,0.987545165253523,0.9871658688284007,0.9871593821633645,0.9765111789874306
KNN,0.985799693211876,0.9860027473964548,0.9857996443259627,0.9857949494291359,0.9714390519303251
LightGBM,0.9867501074457268,0.9871746440451418,0.9867500611481882,0.986742551412285,0.9761280384356184
Logistic Regression,0.5005223784116202,0.562626788952518,0.5005218480118947,0.5107950559395251,0.005946286658849886
Naive Bayes,0.4723488858839027,0.5969532376103299,0.4723500863296926,0.42720139219320163,-0.2689534402954671
Random Forest,0.9871868786929009,0.987691527665192,0.9871868339215206,0.9871782221915515,0.9764689096551693
SGDClassifier,0.826367705483401,0.8346554818949127,0.82636797826275,0.8279433426801518,-115666474262.56816
XGBoost,0.9870226527039634,0.9874407058802472,0.9870226074808386,0.9870154832664209,0.9762825965881348
