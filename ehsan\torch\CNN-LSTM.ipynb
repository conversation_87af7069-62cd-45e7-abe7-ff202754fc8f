{"cells": [{"cell_type": "code", "execution_count": 1, "id": "231e4c51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/40 | TL:0.4851 VA:94.29%\n", "Epoch 2/40 | TL:0.1708 VA:95.24%\n", "Epoch 3/40 | TL:0.1268 VA:95.34%\n", "Epoch 4/40 | TL:0.1085 VA:93.48%\n", "Epoch 5/40 | TL:0.1039 VA:95.54%\n", "Epoch 6/40 | TL:0.0975 VA:95.55%\n", "Epoch 7/40 | TL:0.0936 VA:98.49%\n", "Epoch 8/40 | TL:0.0896 VA:96.87%\n", "Epoch 9/40 | TL:0.0860 VA:98.45%\n", "Epoch 10/40 | TL:0.0836 VA:98.54%\n", "Epoch 11/40 | TL:0.0808 VA:98.43%\n", "Epoch 12/40 | TL:0.0778 VA:96.67%\n", "Epoch 13/40 | TL:0.0767 VA:98.53%\n", "Epoch 14/40 | TL:0.0766 VA:95.96%\n", "Epoch 15/40 | TL:0.0747 VA:98.55%\n", "Epoch 16/40 | TL:0.0733 VA:97.94%\n", "Epoch 17/40 | TL:0.0734 VA:96.10%\n", "Epoch 18/40 | TL:0.0645 VA:98.55%\n", "Epoch 19/40 | TL:0.0636 VA:98.07%\n", "Epoch 20/40 | TL:0.0638 VA:98.55%\n", "Epoch 21/40 | TL:0.0627 VA:98.51%\n", "Epoch 22/40 | TL:0.0631 VA:98.49%\n", "Epoch 23/40 | TL:0.0575 VA:98.56%\n", "Epoch 24/40 | TL:0.0575 VA:98.56%\n", "Epoch 25/40 | TL:0.0573 VA:98.58%\n", "Epoch 26/40 | TL:0.0563 VA:98.56%\n", "Epoch 27/40 | TL:0.0563 VA:98.43%\n", "Epoch 28/40 | TL:0.0563 VA:98.58%\n", "Epoch 29/40 | TL:0.0564 VA:98.56%\n", "Epoch 30/40 | TL:0.0562 VA:98.55%\n", "Epoch 31/40 | TL:0.0559 VA:98.54%\n", "Epoch 32/40 | TL:0.0553 VA:98.56%\n", "Epoch 33/40 | TL:0.0535 VA:98.57%\n", "Epoch 34/40 | TL:0.0532 VA:98.56%\n", "Epoch 35/40 | TL:0.0530 VA:98.57%\n", "Epoch 36/40 | TL:0.0530 VA:98.59%\n", "Epoch 37/40 | TL:0.0529 VA:98.59%\n", "Epoch 38/40 | TL:0.0533 VA:98.57%\n", "Epoch 39/40 | TL:0.0531 VA:98.59%\n", "Epoch 40/40 | TL:0.0528 VA:98.58%\n", "              precision    recall  f1-score   support\n", "\n", "      Benign       1.00      1.00      1.00     57238\n", "      Botnet       1.00      1.00      1.00     57238\n", " Brute-force       0.94      1.00      0.97     57238\n", " DDoS attack       1.00      1.00      1.00     57239\n", "  DoS attack       1.00      0.93      0.96     57238\n", "\n", "    accuracy                           0.99    286191\n", "   macro avg       0.99      0.99      0.99    286191\n", "weighted avg       0.99      0.99      0.99    286191\n", "\n"]}], "source": ["# import os\n", "# import torch\n", "# import torch.nn as nn\n", "# import torch.nn.functional as F\n", "# import pandas as pd\n", "# import numpy as np\n", "# import joblib\n", "# import matplotlib.pyplot as plt\n", "# from torch.utils.data import DataLoader, TensorDataset\n", "# from sklearn.preprocessing import LabelEncoder, label_binarize\n", "# from sklearn.metrics import (\n", "#     classification_report,\n", "#     confusion_matrix,\n", "#     roc_curve,\n", "#     auc,\n", "#     precision_recall_curve,\n", "#     average_precision_score,\n", "#     f1_score,\n", "#     precision_score,\n", "#     recall_score\n", "# )\n", "# from sklearn.model_selection import train_test_split\n", "\n", "# # Device Configuration\n", "# device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# # Ensure output directories exist\n", "# def ensure_dirs():\n", "#     base_dir = \"CNN-LSTM\"\n", "#     plots_dir = os.path.join(base_dir, \"plots\")\n", "#     os.makedirs(base_dir, exist_ok=True)\n", "#     os.makedirs(plots_dir, exist_ok=True)\n", "#     return base_dir, plots_dir\n", "\n", "# # Load dataset with persistent LabelEncoder and validation split\n", "# def load_data(train_path, test_path, test_size=0.2):\n", "#     train_df = pd.read_csv(train_path)\n", "#     test_df = pd.read_csv(test_path)\n", "\n", "#     # Drop 'Threat' column if present\n", "#     for df in (train_df, test_df):\n", "#         if 'Threat' in df.columns:\n", "#             df.drop(columns=[\"Threat\"], inplace=True)\n", "\n", "#     # Initialize or load label encoder\n", "#     try:\n", "#         label_encoder = joblib.load(\"CNN-LSTM/label_encoder.pkl\")\n", "#     except FileNotFoundError:\n", "#         label_encoder = LabelEncoder()\n", "#         label_encoder.fit(train_df[\"Label\"])\n", "#         joblib.dump(label_encoder, \"CNN-LSTM/label_encoder.pkl\")\n", "\n", "#     # Encode labels\n", "#     train_df[\"Label\"] = label_encoder.transform(train_df[\"Label\"])\n", "#     test_df[\"Label\"] = label_encoder.transform(test_df[\"Label\"])\n", "\n", "#     # Split features and labels\n", "#     X_train, y_train = train_df.drop(columns=[\"Label\"]).values, train_df[\"Label\"].values\n", "#     X_test, y_test   = test_df.drop(columns=[\"Label\"]).values, test_df[\"Label\"].values\n", "\n", "#     # Create validation split\n", "#     X_train, X_val, y_train, y_val = train_test_split(\n", "#         X_train, y_train,\n", "#         test_size=test_size,\n", "#         random_state=42,\n", "#         stratify=y_train\n", "#     )\n", "\n", "#     return X_train, y_train, X_val, y_val, X_test, y_test, label_encoder\n", "\n", "# # CNN-LSTM Model Definition\n", "# class CNN_LSTM_Model(nn.Module):\n", "#     def __init__(self, input_size, num_classes):\n", "#         super(CNN_LSTM_Model, self).__init__()\n", "#         self.conv1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)\n", "#         self.bn1   = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "#         self.dropout = nn.Dropout(0.3)\n", "#         self.lstm  = nn.LSTM(input_size=64, hidden_size=128, num_layers=1, batch_first=True)\n", "#         self.fc    = nn.Linear(128, num_classes)\n", "\n", "#     def forward(self, x):\n", "#         x = self.conv1(x)\n", "#         x = <PERSON>.relu(self.bn1(x))\n", "#         x = self.dropout(x)\n", "#         # reshape for LSTM\n", "#         x = x.permute(0, 2, 1)  # (batch, seq_len, features)\n", "#         _, (h_n, _) = self.lstm(x)\n", "#         return self.fc(h_n[-1])\n", "\n", "# # Training with Early Stopping\n", "\n", "# def train_model(model, train_loader, val_loader, optimizer, criterion, scheduler, epochs, device, patience=5):\n", "#     history = {\"train_loss\": [], \"val_loss\": [], \"train_acc\": [], \"val_acc\": []}\n", "#     best_val_loss = float('inf')\n", "#     patience_counter = 0\n", "\n", "#     for epoch in range(1, epochs+1):\n", "#         # Training\n", "#         model.train()\n", "#         total_loss = correct = total = 0\n", "#         for X_batch, y_batch in train_loader:\n", "#             X_batch = X_batch.to(device).unsqueeze(1)\n", "#             y_batch = y_batch.to(device)\n", "#             optimizer.zero_grad()\n", "#             outputs = model(X_batch)\n", "#             loss = criterion(outputs, y_batch)\n", "#             loss.backward()\n", "#             optimizer.step()\n", "#             total_loss += loss.item()\n", "#             preds = outputs.argmax(dim=1)\n", "#             correct += (preds == y_batch).sum().item()\n", "#             total += y_batch.size(0)\n", "#         train_loss = total_loss / len(train_loader)\n", "#         train_acc = 100 * correct / total\n", "\n", "#         # Validation\n", "#         model.eval()\n", "#         val_loss = val_correct = val_total = 0\n", "#         with torch.no_grad():\n", "#             for X_batch, y_batch in val_loader:\n", "#                 X_batch = X_batch.to(device).unsqueeze(1)\n", "#                 y_batch = y_batch.to(device)\n", "#                 outputs = model(X_batch)\n", "#                 loss = criterion(outputs, y_batch)\n", "#                 val_loss += loss.item()\n", "#                 preds = outputs.argmax(dim=1)\n", "#                 val_correct += (preds == y_batch).sum().item()\n", "#                 val_total += y_batch.size(0)\n", "#         val_loss /= len(val_loader)\n", "#         val_acc = 100 * val_correct / val_total\n", "\n", "#         # Record history\n", "#         history['train_loss'].append(train_loss)\n", "#         history['val_loss'].append(val_loss)\n", "#         history['train_acc'].append(train_acc)\n", "#         history['val_acc'].append(val_acc)\n", "\n", "#         print(f\"Epoch {epoch}/{epochs} | TL: {train_loss:.4f} | VL: {val_loss:.4f} | TA: {train_acc:.2f}% | VA: {val_acc:.2f}%\")\n", "\n", "#         # Scheduler step\n", "#         scheduler.step(val_loss)\n", "\n", "#         # Early stopping check\n", "#         if val_loss < best_val_loss:\n", "#             best_val_loss = val_loss\n", "#             patience_counter = 0\n", "#         else:\n", "#             patience_counter += 1\n", "#             if patience_counter >= patience:\n", "#                 print(f\"Early stopping at epoch {epoch}\")\n", "#                 break\n", "\n", "#     return history\n", "\n", "# # Plot and save training history\n", "\n", "# def plot_training_history(history, plots_dir):\n", "#     epochs = range(1, len(history['train_loss'])+1)\n", "#     plt.figure()\n", "#     plt.plot(epochs, history['train_loss'], label='Train Loss')\n", "#     plt.plot(epochs, history['val_loss'], label='Val Loss')\n", "#     plt.plot(epochs, history['train_acc'], label='Train Acc')\n", "#     plt.plot(epochs, history['val_acc'], label='Val Acc')\n", "#     plt.xlabel('Epoch')\n", "#     plt.legend()\n", "#     plt.title('Training & Validation Metrics')\n", "#     plt.savefig(os.path.join(plots_dir, 'training_history.png'))\n", "#     plt.close()\n", "\n", "# # Evaluation with ROC & PR curves\n", "\n", "# def evaluate_model(model, test_loader, device, label_encoder, plots_dir):\n", "#     model.eval()\n", "#     y_true, y_pred, y_score = [], [], []\n", "#     with torch.no_grad():\n", "#         for X_batch, y_batch in test_loader:\n", "#             X_batch = X_batch.to(device).unsqueeze(1)\n", "#             logits = model(X_batch)\n", "#             probs = F.softmax(logits, dim=1).cpu().numpy()\n", "#             preds = probs.argmax(axis=1)\n", "#             y_true.extend(y_batch.numpy())\n", "#             y_pred.extend(preds)\n", "#             y_score.extend(probs)\n", "\n", "#     y_true = np.array(y_true)\n", "#     y_pred = np.array(y_pred)\n", "#     y_score = np.array(y_score)\n", "#     classes = label_encoder.classes_\n", "#     n_classes = len(classes)\n", "\n", "#     # Print classification report\n", "#     print(classification_report(y_true, y_pred, target_names=classes))\n", "\n", "#     # Save confusion matrix\n", "#     cm = confusion_matrix(y_true, y_pred)\n", "#     plt.figure()\n", "#     plt.imshow(cm, cmap='Blues')\n", "#     plt.title('Confusion Matrix')\n", "#     plt.colorbar()\n", "#     ticks = np.arange(n_classes)\n", "#     plt.xticks(ticks, classes, rotation=45)\n", "#     plt.yticks(ticks, classes)\n", "#     plt.xlabel('Predicted')\n", "#     plt.ylabel('Actual')\n", "#     plt.savefig(os.path.join(plots_dir, 'confusion_matrix.png'))\n", "#     plt.close()\n", "\n", "#     # Binarize labels for multi-class metrics\n", "#     y_true_bin = label_binarize(y_true, classes=range(n_classes))\n", "\n", "#     # ROC Curves\n", "#     plt.figure()\n", "#     for i in range(n_classes):\n", "#         fpr, tpr, _ = roc_curve(y_true_bin[:, i], y_score[:, i])\n", "#         plt.plot(fpr, tpr, label=f'{classes[i]} (AUC={auc(fpr, tpr):.2f})')\n", "#     plt.plot([0,1], [0,1], 'k--')\n", "#     plt.title('ROC Curves')\n", "#     plt.xlabel('FPR')\n", "#     plt.ylabel('TPR')\n", "#     plt.legend(loc='best')\n", "#     plt.savefig(os.path.join(plots_dir, 'roc_curves.png'))\n", "#     plt.close()\n", "\n", "#     # Precision-<PERSON><PERSON><PERSON>\n", "#     plt.figure()\n", "#     for i in range(n_classes):\n", "#         precision, recall, _ = precision_recall_curve(y_true_bin[:, i], y_score[:, i])\n", "#         plt.plot(recall, precision, label=f'{classes[i]} (AP={average_precision_score(y_true_bin[:, i], y_score[:, i]):.2f})')\n", "#     plt.title('Precision-<PERSON><PERSON><PERSON>ves')\n", "#     plt.xlabel('Recall')\n", "#     plt.ylabel('Precision')\n", "#     plt.legend(loc='best')\n", "#     plt.savefig(os.path.join(plots_dir, 'pr_curves.png'))\n", "#     plt.close()\n", "\n", "#     # Print weighted metrics\n", "#     print(f\"Weighted F1-score: {f1_score(y_true, y_pred, average='weighted'):.4f}\")\n", "#     print(f\"Weighted Precision: {precision_score(y_true, y_pred, average='weighted'):.4f}\")\n", "#     print(f\"Weighted Recall: {recall_score(y_true, y_pred, average='weighted'):.4f}\")\n", "\n", "# # Main execution\n", "# def main(train_path, test_path, epochs=40, batch_size=256, learning_rate=0.001):\n", "#     base_dir, plots_dir = ensure_dirs()\n", "#     X_train, y_train, X_val, y_val, X_test, y_test, label_encoder = load_data(train_path, test_path)\n", "\n", "#     # Convert to tensors\n", "#     X_train = torch.tensor(X_train, dtype=torch.float32)\n", "#     y_train = torch.tensor(y_train, dtype=torch.long)\n", "#     X_val   = torch.tensor(X_val, dtype=torch.float32)\n", "#     y_val   = torch.tensor(y_val, dtype=torch.long)\n", "#     X_test  = torch.tensor(X_test, dtype=torch.float32)\n", "#     y_test  = torch.tensor(y_test, dtype=torch.long)\n", "\n", "#     train_loader = DataLoader(TensorDataset(X_train, y_train), batch_size=batch_size, shuffle=True)\n", "#     val_loader   = DataLoader(TensorDataset(X_val, y_val), batch_size=batch_size, shuffle=False)\n", "#     test_loader  = DataLoader(TensorDataset(X_test, y_test), batch_size=batch_size, shuffle=False)\n", "\n", "#     model = CNN_LSTM_Model(input_size=X_train.shape[1], num_classes=len(label_encoder.classes_)).to(device)\n", "#     optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "#     criterion = nn.CrossEntropyLoss()\n", "#     scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=3, factor=0.5)\n", "\n", "#     history = train_model(model, train_loader, val_loader, optimizer, criterion, scheduler, epochs, device)\n", "#     plot_training_history(history, plots_dir)\n", "#     evaluate_model(model, test_loader, device, label_encoder, plots_dir)\n", "\n", "#     torch.save(model.state_dict(), os.path.join(base_dir, 'model.pth'))\n", "\n", "# if __name__ == '__main__':\n", "#     main('train_data.csv', 'test_data.csv', epochs=40)\n", "#################################################################################################################\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.preprocessing import LabelEncoder, label_binarize\n", "from sklearn.metrics import (\n", "    classification_report,\n", "    confusion_matrix,\n", "    roc_curve,\n", "    auc,\n", "    precision_recall_curve,\n", "    average_precision_score,\n", "    f1_score,\n", "    precision_score,\n", "    recall_score\n", ")\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Device Configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# Directories\n", "def ensure_dirs():\n", "    base_dir = \"CNN-LSTM\"\n", "    plots_dir = os.path.join(base_dir, \"plots\")\n", "    os.makedirs(base_dir, exist_ok=True)\n", "    os.makedirs(plots_dir, exist_ok=True)\n", "    return base_dir, plots_dir\n", "\n", "# Data Loading\n", "\n", "def load_data(train_path, test_path, test_size=0.2):\n", "    train_df = pd.read_csv(train_path)\n", "    test_df = pd.read_csv(test_path)\n", "    for df in (train_df, test_df):\n", "        if 'Threat' in df.columns:\n", "            df.drop(columns=['Threat'], inplace=True)\n", "    try:\n", "        le = joblib.load(\"CNN-LSTM/label_encoder.pkl\")\n", "    except FileNotFoundError:\n", "        le = LabelEncoder()\n", "        le.fit(train_df['Label'])\n", "        joblib.dump(le, \"CNN-LSTM/label_encoder.pkl\")\n", "    train_df['Label'] = le.transform(train_df['Label'])\n", "    test_df['Label']  = le.transform(test_df['Label'])\n", "    X_train, y_train = train_df.drop(columns=['Label']).values, train_df['Label'].values\n", "    X_test, y_test   = test_df.drop(columns=['Label']).values,  test_df['Label'].values\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X_train, y_train, test_size=test_size, random_state=42, stratify=y_train\n", "    )\n", "    return X_train, y_train, X_val, y_val, X_test, y_test, le\n", "\n", "# Model Definition\n", "\n", "class CNN_LSTM_Model(nn.Module):\n", "    def __init__(self, input_size, num_classes):\n", "        super().__init__()\n", "        self.conv1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.drop = nn.Dropout(0.3)\n", "        self.lstm = nn.LSTM(input_size=64, hidden_size=128, batch_first=True)\n", "        self.fc = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, x):\n", "        x = self.conv1(x)\n", "        x = <PERSON>.relu(self.bn1(x))\n", "        x = self.drop(x)\n", "        x = x.permute(0, 2, 1)\n", "        _, (hn, _) = self.lstm(x)\n", "        return self.fc(hn[-1])\n", "\n", "# Training\n", "\n", "def train_model(model, train_loader, val_loader, optimizer, criterion, scheduler, epochs, device, patience=5):\n", "    history = {'train_loss':[], 'val_loss':[], 'train_acc':[], 'val_acc':[]}\n", "    best_loss = float('inf'); counter=0\n", "    for e in range(1, epochs+1):\n", "        model.train()\n", "        tloss=0; tcorrect=0; ttotal=0\n", "        for X,y in train_loader:\n", "            X,y = X.to(device).unsqueeze(1), y.to(device)\n", "            optimizer.zero_grad()\n", "            out = model(X)\n", "            loss = criterion(out,y)\n", "            loss.backward(); optimizer.step()\n", "            tloss+=loss.item()\n", "            preds = out.argmax(1)\n", "            tcorrect+=(preds==y).sum().item(); ttotal+=y.size(0)\n", "        train_loss=tloss/len(train_loader); train_acc=100*tcorrect/ttotal\n", "        model.eval(); vloss=0; vcorrect=0; vtotal=0\n", "        with torch.no_grad():\n", "            for X,y in val_loader:\n", "                X,y = X.to(device).unsqueeze(1), y.to(device)\n", "                out = model(X)\n", "                vloss+=criterion(out,y).item()\n", "                preds=out.argmax(1)\n", "                vcorrect+=(preds==y).sum().item(); vtotal+=y.size(0)\n", "        val_loss=vloss/len(val_loader); val_acc=100*vcorrect/vtotal\n", "        history['train_loss'].append(train_loss)\n", "        history['val_loss'].append(val_loss)\n", "        history['train_acc'].append(train_acc)\n", "        history['val_acc'].append(val_acc)\n", "        print(f\"Epoch {e}/{epochs} | TL:{train_loss:.4f} VA:{val_acc:.2f}%\")\n", "        scheduler.step(val_loss)\n", "        if val_loss<best_loss: best_loss, counter=val_loss,0\n", "        else:\n", "            counter+=1\n", "            if counter>=patience:\n", "                print(f\"Early stopping at epoch {e}\")\n", "                break\n", "    return history\n", "\n", "# Plotting Functions\n", "\n", "def plot_loss(history, plots_dir):\n", "    epochs = range(1, len(history['train_loss'])+1)\n", "    plt.figure(); plt.plot(epochs, history['train_loss'], label='Train Loss'); plt.plot(epochs, history['val_loss'], label='Val Loss')\n", "    plt.xlabel('Epoch'); plt.ylabel('Loss'); plt.title('Loss vs Epoch'); plt.legend()\n", "    plt.savefig(os.path.join(plots_dir,'loss_vs_epoch.png')); plt.close()\n", "\n", "def plot_acc(history, plots_dir):\n", "    epochs = range(1, len(history['train_acc'])+1)\n", "    plt.figure(); plt.plot(epochs, history['train_acc'], label='Train Acc'); plt.plot(epochs, history['val_acc'], label='Val Acc')\n", "    plt.xlabel('Epoch'); plt.ylabel('Accuracy'); plt.title('Accuracy vs Epoch'); plt.legend()\n", "    plt.savefig(os.path.join(plots_dir,'acc_vs_epoch.png')); plt.close()\n", "\n", "# Additional Evaluation Plots\n", "\n", "def plot_confusion_matrix(cm, classes, plots_dir, normalize=False):\n", "    if normalize:\n", "        cm = cm.astype('float')/cm.sum(axis=1)[:,None]\n", "    plt.figure(figsize=(8,6)); sns.heatmap(cm, annot=True, fmt='.2f' if normalize else 'd',\n", "        xticklabels=classes, yticklabels=classes, cmap='Blues')\n", "    plt.xlabel('Pred'); plt.ylabel('True'); plt.title('Normalized Confusion Matrix' if normalize else 'Confusion Matrix')\n", "    name = 'conf_matrix_norm.png' if normalize else 'conf_matrix.png'\n", "    plt.savefig(os.path.join(plots_dir,name)); plt.close()\n", "\n", "def plot_classification_report_heatmap(y_true, y_pred, classes, plots_dir):\n", "    cr = classification_report(y_true,y_pred,labels=range(len(classes)),target_names=classes,output_dict=True)\n", "    df = pd.DataFrame(cr).T.iloc[:-1, :3]  # drop accuracy row\n", "    plt.figure(figsize=(6,4)); sns.heatmap(df, annot=True, cmap='Greens')\n", "    plt.title('Classification Report'); plt.savefig(os.path.join(plots_dir,'classification_report_heatmap.png')); plt.close()\n", "\n", "def plot_prf_barchart(y_true, y_pred, classes, plots_dir):\n", "    pr = precision_score(y_true,y_pred, labels=range(len(classes)), average=None)\n", "    rc = recall_score(y_true,y_pred,    labels=range(len(classes)), average=None)\n", "    f1 = f1_score(y_true,y_pred,        labels=range(len(classes)), average=None)\n", "    x = np.arange(len(classes)); width=0.2\n", "    plt.figure(figsize=(8,5))\n", "    plt.bar(x-width, pr, width, label='Precision')\n", "    plt.bar(x, rc, width, label='Recall')\n", "    plt.bar(x+width, f1, width, label='F1')\n", "    plt.xticks(x, classes, rotation=45); plt.ylabel('Score'); plt.title('Per-Class Metrics'); plt.legend()\n", "    plt.tight_layout(); plt.savefig(os.path.join(plots_dir,'prf_barchart.png')); plt.close()\n", "\n", "# Main Execution\n", "def main(train_path, test_path, epochs=40, batch_size=256, lr=0.001):\n", "    base, plots = ensure_dirs()\n", "    X_train,y_train,X_val,y_val,X_test,y_test,le = load_data(train_path,test_path)\n", "    # tensors\n", "    X_train = torch.tensor(X_train,dtype=torch.float32); y_train=torch.tensor(y_train)\n", "    X_val   = torch.tensor(X_val,dtype=torch.float32);   y_val  =torch.tensor(y_val)\n", "    X_test  = torch.tensor(X_test,dtype=torch.float32);  y_test =torch.tensor(y_test)\n", "    # loaders\n", "    tr = DataLoader(TensorDataset(X_train,y_train),batch_size=batch_size,shuffle=True)\n", "    va = DataLoader(TensorDataset(X_val,y_val),    batch_size=batch_size)\n", "    te = DataLoader(TensorDataset(X_test,y_test),  batch_size=batch_size)\n", "\n", "    model = CNN_LSTM_Model(input_size=X_train.shape[1], num_classes=len(le.classes_)).to(device)\n", "    opt = torch.optim.Adam(model.parameters(), lr=lr)\n", "    crit= nn.CrossEntropyLoss()\n", "    sch = torch.optim.lr_scheduler.ReduceLROnPlateau(opt,mode='min',patience=3,factor=0.5)\n", "\n", "    hist = train_model(model,tr,va,opt,crit,sch,epochs,device)\n", "    plot_loss(hist,plots); plot_acc(hist,plots)\n", "\n", "    # Evaluate and extra plots\n", "    # gather predictions\n", "    model.eval(); y_t,y_p, y_sc = [],[],[]\n", "    with torch.no_grad():\n", "        for X,y in te:\n", "            X = X.to(device).unsqueeze(1)\n", "            logits = model(X); probs=F.softmax(logits,1).cpu().numpy()\n", "            preds = np.argmax(probs,axis=1)\n", "            y_t.extend(y.numpy()); y_p.extend(preds)\n", "    y_t=np.array(y_t); y_p=np.array(y_p); classes=le.classes_\n", "    # confusion matrices\n", "    cm = confusion_matrix(y_t,y_p)\n", "    plot_confusion_matrix(cm,classes,plots,normalize=False)\n", "    plot_confusion_matrix(cm,classes,plots,normalize=True)\n", "    # classification report\n", "    print(classification_report(y_t,y_p,target_names=classes))\n", "    plot_classification_report_heatmap(y_t,y_p,classes,plots)\n", "    # per-class bar chart\n", "    plot_prf_barchart(y_t,y_p,classes,plots)\n", "    # ROC & PR curves (unchanged)\n", "    # ... existing roc/pr code ...\n", "    torch.save(model.state_dict(),os.path.join(base,'model.pth'))\n", "\n", "if __name__=='__main__':\n", "    main('train_data.csv','test_data.csv',epochs=40)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f644ef4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved evaluation metrics to CNN-LSTM/metrics.txt\n"]}], "source": ["#36\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.preprocessing import LabelEncoder, label_binarize\n", "from sklearn.metrics import (\n", "    classification_report,\n", "    confusion_matrix,\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    f1_score,\n", "    roc_auc_score,\n", "    average_precision_score\n", ")\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Device Configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# Data Loading\n", "def load_data(train_path, test_path, test_size=0.2):\n", "    train_df = pd.read_csv(train_path)\n", "    test_df = pd.read_csv(test_path)\n", "    for df in (train_df, test_df):\n", "        if 'Threat' in df.columns:\n", "            df.drop(columns=['Threat'], inplace=True)\n", "    try:\n", "        le = joblib.load(\"CNN-LSTM/label_encoder.pkl\")\n", "    except FileNotFoundError:\n", "        le = LabelEncoder().fit(train_df['Label'])\n", "        joblib.dump(le, \"CNN-LSTM/label_encoder.pkl\")\n", "    train_df['Label'] = le.transform(train_df['Label'])\n", "    test_df['Label'] = le.transform(test_df['Label'])\n", "    X = train_df.drop(columns=['Label']).values\n", "    y = train_df['Label'].values\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X, y, test_size=test_size, random_state=42, stratify=y\n", "    )\n", "    X_test, y_test = (\n", "        test_df.drop(columns=['Label']).values,\n", "        test_df['Label'].values\n", "    )\n", "    return X_train, y_train, X_val, y_val, X_test, y_test, le\n", "\n", "# Model Definition\n", "class CNN_LSTM_Model(nn.Module):\n", "    def __init__(self, input_size, num_classes):\n", "        super().__init__()\n", "        self.conv1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.drop = nn.Dropout(0.3)\n", "        self.lstm = nn.LSTM(input_size=64, hidden_size=128, batch_first=True)\n", "        self.fc = nn.Linear(128, num_classes)\n", "    def forward(self, x):\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = self.drop(x)\n", "        x = x.permute(0, 2, 1)\n", "        _, (hn, _) = self.lstm(x)\n", "        return self.fc(hn[-1])\n", "\n", "# Training Function\n", "def train_model(model, train_loader, val_loader, optimizer, criterion, scheduler,\n", "                epochs, device, patience=5):\n", "    best_loss = float('inf'); counter = 0\n", "    for e in range(1, epochs+1):\n", "        model.train()\n", "        train_loss = train_correct = train_total = 0\n", "        for X,y in train_loader:\n", "            X,y = X.to(device).unsqueeze(1), y.to(device)\n", "            optimizer.zero_grad()\n", "            out = model(X)\n", "            loss = criterion(out,y)\n", "            loss.backward(); optimizer.step()\n", "            train_loss += loss.item()\n", "            preds = out.argmax(1)\n", "            train_correct += (preds==y).sum().item(); train_total += y.size(0)\n", "        val_loss = val_correct = val_total = 0\n", "        model.eval()\n", "        with torch.no_grad():\n", "            for X,y in val_loader:\n", "                X,y = X.to(device).unsqueeze(1), y.to(device)\n", "                out = model(X)\n", "                loss = criterion(out,y)\n", "                val_loss += loss.item()\n", "                preds = out.argmax(1)\n", "                val_correct += (preds==y).sum().item(); val_total += y.size(0)\n", "        scheduler.step(val_loss)\n", "        if val_loss < best_loss:\n", "            best_loss, counter = val_loss, 0\n", "        else:\n", "            counter += 1\n", "            if counter >= patience:\n", "                print(f\"Early stopping at epoch {e}\")\n", "                break\n", "    return model\n", "\n", "# Evaluation & Save Metrics\n", "def evaluate_and_save(model, test_loader, device, label_encoder):\n", "    model.eval()\n", "    y_true, y_pred, probs = [], [], []\n", "    with torch.no_grad():\n", "        for X,y in test_loader:\n", "            X = X.to(device).unsqueeze(1)\n", "            out = model(X)\n", "            prob = <PERSON>.softmax(out, dim=1)\n", "            preds = prob.argmax(1)\n", "            y_true.extend(y.numpy()); y_pred.extend(preds.cpu().numpy())\n", "            probs.append(prob.cpu().numpy())\n", "    probs = np.vstack(probs)\n", "    classes = list(label_encoder.classes_)\n", "    y_true = np.array(y_true); y_pred = np.array(y_pred)\n", "    acc = accuracy_score(y_true, y_pred)\n", "    prec = precision_score(y_true, y_pred, average='weighted')\n", "    rec = recall_score(y_true, y_pred, average='weighted')\n", "    f1 = f1_score(y_true, y_pred, average='weighted')\n", "    y_bin = label_binarize(y_true, classes=range(len(classes)))\n", "    roc_auc = roc_auc_score(y_bin, probs, average='macro', multi_class='ovr')\n", "    ap_scores = {cls: average_precision_score(y_bin[:,i], probs[:,i])\n", "                 for i,cls in enumerate(classes)}\n", "    os.makedirs(\"CNN-LSTM\", exist_ok=True)\n", "    with open(\"CNN-LSTM/metrics.txt\", \"w\") as f:\n", "        f.write(f\"Accuracy: {acc:.4f}\\n\")\n", "        f.write(f\"Precision (weighted): {prec:.4f}\\n\")\n", "        f.write(f\"Recall (weighted): {rec:.4f}\\n\")\n", "        f.write(f\"F1 Score (weighted): {f1:.4f}\\n\")\n", "        f.write(f\"ROC AUC (macro, OVR): {roc_auc:.4f}\\n\\n\")\n", "        f.write(\"Average Precision per Class:\\n\")\n", "        for cls,ap in ap_scores.items(): f.write(f\"  {cls}: {ap:.4f}\\n\")\n", "        f.write(\"\\nClassification Report:\\n\")\n", "        f.write(classification_report(y_true, y_pred, target_names=classes))\n", "        f.write(\"\\nConfusion Matrix:\\n\")\n", "        f.write(np.array2string(confusion_matrix(y_true, y_pred)))\n", "    print(\"Saved evaluation metrics to CNN-LSTM/metrics.txt\")\n", "\n", "# Main\n", "def main(train_path, test_path, epochs=40, batch_size=256, lr=1e-3):\n", "    X_tr,y_tr,X_val,y_val,X_te,y_te,le = load_data(train_path, test_path)\n", "    # Correct tensor dtype specification\n", "    tr_loader = DataLoader(\n", "        TensorDataset(\n", "            torch.tensor(X_tr, dtype=torch.float32),\n", "            torch.tensor(y_tr, dtype=torch.long)\n", "        ), batch_size=batch_size, shuffle=True\n", "    )\n", "    val_loader = DataLoader(\n", "        TensorDataset(\n", "            torch.tensor(X_val, dtype=torch.float32),\n", "            torch.tensor(y_val, dtype=torch.long)\n", "        ), batch_size=batch_size\n", "    )\n", "    test_loader = DataLoader(\n", "        TensorDataset(\n", "            torch.tensor(X_te, dtype=torch.float32),\n", "            torch.tensor(y_te, dtype=torch.long)\n", "        ), batch_size=batch_size\n", "    )\n", "    model = CNN_LSTM_Model(X_tr.shape[1], len(le.classes_)).to(device)\n", "    opt = torch.optim.Adam(model.parameters(), lr=lr)\n", "    crit = nn.CrossEntropyLoss()\n", "    sch = torch.optim.lr_scheduler.ReduceLROnPlateau(opt, mode='min', patience=3, factor=0.5)\n", "    trained = train_model(model, tr_loader, val_loader, opt, crit, sch, epochs, device)\n", "    evaluate_and_save(trained, test_loader, device, le)\n", "    torch.save(trained.state_dict(), os.path.join('CNN-LSTM','model.pth'))\n", "\n", "if __name__=='__main__':\n", "    main('train_data.csv','test_data.csv', epochs=40)"]}, {"cell_type": "code", "execution_count": 4, "id": "05ea8d14", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Early stopping at epoch 32\n", "Saved evaluation metrics to CNN-LSTM/metrics.txt\n"]}], "source": ["#10\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import pandas as pd\n", "import numpy as np\n", "import joblib\n", "\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from sklearn.preprocessing import LabelEncoder, label_binarize\n", "from sklearn.metrics import (\n", "    classification_report,\n", "    confusion_matrix,\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    f1_score,\n", "    roc_auc_score,\n", "    average_precision_score\n", ")\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Device Configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# Data Loading\n", "def load_data(train_path, test_path, test_size=0.2):\n", "    train_df = pd.read_csv(train_path)\n", "    test_df = pd.read_csv(test_path)\n", "    for df in (train_df, test_df):\n", "        if 'Threat' in df.columns:\n", "            df.drop(columns=['Threat'], inplace=True)\n", "    try:\n", "        le = joblib.load(\"CNN-LSTM/label_encoder.pkl\")\n", "    except FileNotFoundError:\n", "        le = LabelEncoder().fit(train_df['Label'])\n", "        joblib.dump(le, \"CNN-LSTM/label_encoder.pkl\")\n", "    train_df['Label'] = le.transform(train_df['Label'])\n", "    test_df['Label'] = le.transform(test_df['Label'])\n", "    X = train_df.drop(columns=['Label']).values\n", "    y = train_df['Label'].values\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X, y, test_size=test_size, random_state=42, stratify=y\n", "    )\n", "    X_test, y_test = (\n", "        test_df.drop(columns=['Label']).values,\n", "        test_df['Label'].values\n", "    )\n", "    return X_train, y_train, X_val, y_val, X_test, y_test, le\n", "\n", "# Model Definition\n", "class CNN_LSTM_Model(nn.Module):\n", "    def __init__(self, input_size, num_classes):\n", "        super().__init__()\n", "        self.conv1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.drop = nn.Dropout(0.3)\n", "        self.lstm = nn.LSTM(input_size=64, hidden_size=128, batch_first=True)\n", "        self.fc = nn.Linear(128, num_classes)\n", "    def forward(self, x):\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = self.drop(x)\n", "        x = x.permute(0, 2, 1)\n", "        _, (hn, _) = self.lstm(x)\n", "        return self.fc(hn[-1])\n", "\n", "# Training Function\n", "def train_model(model, train_loader, val_loader, optimizer, criterion, scheduler,\n", "                epochs, device, patience=5):\n", "    best_loss = float('inf'); counter = 0\n", "    for e in range(1, epochs+1):\n", "        model.train()\n", "        train_loss = train_correct = train_total = 0\n", "        for X,y in train_loader:\n", "            X,y = X.to(device).unsqueeze(1), y.to(device)\n", "            optimizer.zero_grad()\n", "            out = model(X)\n", "            loss = criterion(out,y)\n", "            loss.backward(); optimizer.step()\n", "            train_loss += loss.item()\n", "            preds = out.argmax(1)\n", "            train_correct += (preds==y).sum().item(); train_total += y.size(0)\n", "        val_loss = val_correct = val_total = 0\n", "        model.eval()\n", "        with torch.no_grad():\n", "            for X,y in val_loader:\n", "                X,y = X.to(device).unsqueeze(1), y.to(device)\n", "                out = model(X)\n", "                loss = criterion(out,y)\n", "                val_loss += loss.item()\n", "                preds = out.argmax(1)\n", "                val_correct += (preds==y).sum().item(); val_total += y.size(0)\n", "        scheduler.step(val_loss)\n", "        if val_loss < best_loss:\n", "            best_loss, counter = val_loss, 0\n", "        else:\n", "            counter += 1\n", "            if counter >= patience:\n", "                print(f\"Early stopping at epoch {e}\")\n", "                break\n", "    return model\n", "\n", "# Evaluation & Save Metrics\n", "def evaluate_and_save(model, test_loader, device, label_encoder):\n", "    model.eval()\n", "    y_true, y_pred, probs = [], [], []\n", "    with torch.no_grad():\n", "        for X,y in test_loader:\n", "            X = X.to(device).unsqueeze(1)\n", "            out = model(X)\n", "            prob = <PERSON>.softmax(out, dim=1)\n", "            preds = prob.argmax(1)\n", "            y_true.extend(y.numpy()); y_pred.extend(preds.cpu().numpy())\n", "            probs.append(prob.cpu().numpy())\n", "    probs = np.vstack(probs)\n", "    classes = list(label_encoder.classes_)\n", "    y_true = np.array(y_true); y_pred = np.array(y_pred)\n", "    acc = accuracy_score(y_true, y_pred)\n", "    prec = precision_score(y_true, y_pred, average='weighted')\n", "    rec = recall_score(y_true, y_pred, average='weighted')\n", "    f1 = f1_score(y_true, y_pred, average='weighted')\n", "    y_bin = label_binarize(y_true, classes=range(len(classes)))\n", "    roc_auc = roc_auc_score(y_bin, probs, average='macro', multi_class='ovr')\n", "    ap_scores = {cls: average_precision_score(y_bin[:,i], probs[:,i])\n", "                 for i,cls in enumerate(classes)}\n", "    os.makedirs(\"CNN-LSTM\", exist_ok=True)\n", "    with open(\"CNN-LSTM/metrics.txt\", \"w\") as f:\n", "        f.write(f\"Accuracy: {acc:.4f}\\n\")\n", "        f.write(f\"Precision (weighted): {prec:.4f}\\n\")\n", "        f.write(f\"Recall (weighted): {rec:.4f}\\n\")\n", "        f.write(f\"F1 Score (weighted): {f1:.4f}\\n\")\n", "        f.write(f\"ROC AUC (macro, OVR): {roc_auc:.4f}\\n\\n\")\n", "        f.write(\"Average Precision per Class:\\n\")\n", "        for cls,ap in ap_scores.items(): f.write(f\"  {cls}: {ap:.4f}\\n\")\n", "        f.write(\"\\nClassification Report:\\n\")\n", "        f.write(classification_report(y_true, y_pred, target_names=classes))\n", "        f.write(\"\\nConfusion Matrix:\\n\")\n", "        f.write(np.array2string(confusion_matrix(y_true, y_pred)))\n", "    print(\"Saved evaluation metrics to CNN-LSTM/metrics.txt\")\n", "\n", "# Main\n", "def main(train_path, test_path, epochs=40, batch_size=256, lr=1e-3):\n", "    X_tr,y_tr,X_val,y_val,X_te,y_te,le = load_data(train_path, test_path)\n", "    # Correct tensor dtype specification\n", "    tr_loader = DataLoader(\n", "        TensorDataset(\n", "            torch.tensor(X_tr, dtype=torch.float32),\n", "            torch.tensor(y_tr, dtype=torch.long)\n", "        ), batch_size=batch_size, shuffle=True\n", "    )\n", "    val_loader = DataLoader(\n", "        TensorDataset(\n", "            torch.tensor(X_val, dtype=torch.float32),\n", "            torch.tensor(y_val, dtype=torch.long)\n", "        ), batch_size=batch_size\n", "    )\n", "    test_loader = DataLoader(\n", "        TensorDataset(\n", "            torch.tensor(X_te, dtype=torch.float32),\n", "            torch.tensor(y_te, dtype=torch.long)\n", "        ), batch_size=batch_size\n", "    )\n", "    model = CNN_LSTM_Model(X_tr.shape[1], len(le.classes_)).to(device)\n", "    opt = torch.optim.Adam(model.parameters(), lr=lr)\n", "    crit = nn.CrossEntropyLoss()\n", "    sch = torch.optim.lr_scheduler.ReduceLROnPlateau(opt, mode='min', patience=3, factor=0.5)\n", "    trained = train_model(model, tr_loader, val_loader, opt, crit, sch, epochs, device)\n", "    evaluate_and_save(trained, test_loader, device, le)\n", "    torch.save(trained.state_dict(), os.path.join('CNN-LSTM','model.pth'))\n", "\n", "if __name__=='__main__':\n", "    main('train_data.csv','test_data.csv', epochs=40)"]}], "metadata": {"kernelspec": {"display_name": "Python (torch-gpu)", "language": "python", "name": "torch-gpu"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}