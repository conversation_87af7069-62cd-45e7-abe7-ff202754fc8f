{"cells": [{"cell_type": "code", "execution_count": null, "id": "71261879", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "import os\n", "from sklearn.metrics import roc_curve, auc, precision_recall_curve, average_precision_score\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import pandas as pd\n", "from itertools import cycle\n", "from sklearn.inspection import permutation_importance\n", "\n", "def create_visualization_dir(output_dir):\n", "    \"\"\"Create a directory for storing visualizations.\"\"\"\n", "    viz_dir = os.path.join(output_dir, \"model_visualizations\")\n", "    os.makedirs(viz_dir, exist_ok=True)\n", "    return viz_dir\n", "\n", "def plot_learning_curve(training_history, output_dir):\n", "    \"\"\"Plot model learning curve and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    \n", "    # Check what metrics are available in the history object\n", "    metrics = [key for key in training_history.history.keys() if not key.startswith('val_')]\n", "    \n", "    # Plot training & validation accuracy values\n", "    for metric in metrics:\n", "        plt.subplot(1, len(metrics), metrics.index(metric) + 1)\n", "        plt.plot(training_history.history[metric])\n", "        plt.plot(training_history.history[f'val_{metric}'])\n", "        plt.title(f'Model {metric}')\n", "        plt.ylabel(metric)\n", "        plt.xlabel('Epoch')\n", "        plt.legend(['Train', 'Validation'], loc='best')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"learning_curve.png\"), dpi=300)\n", "    plt.close()\n", "\n", "def plot_roc_curve(y_true, y_pred_proba, classes, output_dir):\n", "    \"\"\"Plot ROC curve for multi-class and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    # Convert to one-hot encoding if not already\n", "    if len(y_true.shape) == 1:\n", "        n_classes = len(classes)\n", "        y_true_bin = np.zeros((len(y_true), n_classes))\n", "        for i, c in enumerate(classes):\n", "            y_true_bin[:, i] = (y_true == c)\n", "    else:\n", "        y_true_bin = y_true\n", "        n_classes = y_true_bin.shape[1]\n", "    \n", "    # Compute ROC curve and ROC area for each class\n", "    fpr = {}\n", "    tpr = {}\n", "    roc_auc = {}\n", "    \n", "    plt.figure(figsize=(12, 8))\n", "    colors = cycle(['blue', 'red', 'green', 'purple', 'orange', 'brown', 'pink', 'gray', 'olive', 'cyan'])\n", "    \n", "    for i, color, class_name in zip(range(n_classes), colors, classes):\n", "        fpr[i], tpr[i], _ = roc_curve(y_true_bin[:, i], y_pred_proba[:, i])\n", "        roc_auc[i] = auc(fpr[i], tpr[i])\n", "        \n", "        plt.plot(fpr[i], tpr[i], color=color, lw=2,\n", "                 label=f'ROC curve of {class_name} (area = {roc_auc[i]:.2f})')\n", "    \n", "    plt.plot([0, 1], [0, 1], 'k--', lw=2)\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate')\n", "    plt.ylabel('True Positive Rate')\n", "    plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "    plt.legend(loc=\"lower right\")\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"roc_curve.png\"), dpi=300)\n", "    plt.close()\n", "    \n", "    # For binary classification, also create a simpler ROC curve\n", "    if n_classes == 2:\n", "        plt.figure(figsize=(10, 6))\n", "        plt.plot(fpr[1], tpr[1], 'b-', lw=2, label=f'ROC curve (area = {roc_auc[1]:.2f})')\n", "        plt.plot([0, 1], [0, 1], 'k--', lw=2)\n", "        plt.xlim([0.0, 1.0])\n", "        plt.ylim([0.0, 1.05])\n", "        plt.xlabel('False Positive Rate')\n", "        plt.ylabel('True Positive Rate')\n", "        plt.title('Receiver Operating Characteristic (ROC) Curve - Binary')\n", "        plt.legend(loc=\"lower right\")\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, \"binary_roc_curve.png\"), dpi=300)\n", "        plt.close()\n", "\n", "def plot_precision_recall_curve(y_true, y_pred_proba, classes, output_dir):\n", "    \"\"\"Plot Precision-Recall curve for multi-class and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    # Convert to one-hot encoding if not already\n", "    if len(y_true.shape) == 1:\n", "        n_classes = len(classes)\n", "        y_true_bin = np.zeros((len(y_true), n_classes))\n", "        for i, c in enumerate(classes):\n", "            y_true_bin[:, i] = (y_true == c)\n", "    else:\n", "        y_true_bin = y_true\n", "        n_classes = y_true_bin.shape[1]\n", "    \n", "    # Compute Precision-Recall curve and average precision for each class\n", "    precision = {}\n", "    recall = {}\n", "    avg_precision = {}\n", "    \n", "    plt.figure(figsize=(12, 8))\n", "    colors = cycle(['blue', 'red', 'green', 'purple', 'orange', 'brown', 'pink', 'gray', 'olive', 'cyan'])\n", "    \n", "    for i, color, class_name in zip(range(n_classes), colors, classes):\n", "        precision[i], recall[i], _ = precision_recall_curve(y_true_bin[:, i], y_pred_proba[:, i])\n", "        avg_precision[i] = average_precision_score(y_true_bin[:, i], y_pred_proba[:, i])\n", "        \n", "        plt.plot(recall[i], precision[i], color=color, lw=2,\n", "                 label=f'Precision-Recall curve of {class_name} (AP = {avg_precision[i]:.2f})')\n", "    \n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('Recall')\n", "    plt.ylabel('Precision')\n", "    plt.title('Precision-Recall Curve')\n", "    plt.legend(loc=\"lower left\")\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"precision_recall_curve.png\"), dpi=300)\n", "    plt.close()\n", "    \n", "    # For binary classification, also create a simpler PR curve\n", "    if n_classes == 2:\n", "        plt.figure(figsize=(10, 6))\n", "        plt.plot(recall[1], precision[1], 'b-', lw=2, \n", "                 label=f'Precision-Recall curve (AP = {avg_precision[1]:.2f})')\n", "        plt.xlim([0.0, 1.0])\n", "        plt.ylim([0.0, 1.05])\n", "        plt.xlabel('Recall')\n", "        plt.ylabel('Precision')\n", "        plt.title('Precision-<PERSON><PERSON>l <PERSON>urve - Binary')\n", "        plt.legend(loc=\"lower left\")\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, \"binary_precision_recall_curve.png\"), dpi=300)\n", "        plt.close()\n", "\n", "def plot_confusion_matrix(y_true, y_pred, classes, output_dir, normalize=False):\n", "    \"\"\"\n", "    Plot confusion matrix and save as PNG.\n", "    \n", "    Parameters:\n", "    - y_true: True labels\n", "    - y_pred: Predicted labels\n", "    - classes: Class labels\n", "    - output_dir: Directory to save the PNG\n", "    - normalize: Whether to normalize the confusion matrix\n", "    \"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    cm = confusion_matrix(y_true, y_pred)\n", "    \n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        title_suffix = \"(normalized)\"\n", "        fmt = '.2f'\n", "    else:\n", "        title_suffix = \"\"\n", "        fmt = 'd'\n", "    \n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(cm, annot=True, fmt=fmt, cmap=\"Blues\", \n", "                xticklabels=classes, yticklabels=classes)\n", "    plt.title(f'Confusion Matrix {title_suffix}')\n", "    plt.ylabel('True label')\n", "    plt.xlabel('Predicted label')\n", "    plt.tight_layout()\n", "    \n", "    filename = \"confusion_matrix_normalized.png\" if normalize else \"confusion_matrix.png\"\n", "    plt.savefig(os.path.join(viz_dir, filename), dpi=300)\n", "    plt.close()\n", "    \n", "    # If not normalized, also create a normalized version\n", "    if not normalize:\n", "        plot_confusion_matrix(y_true, y_pred, classes, output_dir, normalize=True)\n", "\n", "def plot_feature_importance(model, feature_names, output_dir, \n", "                            importance_type='gain', max_features=15):\n", "    \"\"\"\n", "    Plot feature importance for tree-based models and save as PNG.\n", "    Works with XGBoost, LightGBM, and other tree-based models with feature_importances_.\n", "    \n", "    For neural networks, use permutation_importance instead (see below function).\n", "    \"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    # Try different ways to get feature importance based on model type\n", "    importance = None\n", "    \n", "    # For sklearn models\n", "    if hasattr(model, 'feature_importances_'):\n", "        importance = model.feature_importances_\n", "        title = \"Feature Importance\"\n", "    \n", "    # For XGBoost\n", "    <PERSON><PERSON> hasattr(model, 'get_booster') and hasattr(model.get_booster(), 'get_score'):\n", "        try:\n", "            importance = model.get_booster().get_score(importance_type=importance_type)\n", "            importance = {feature_names[int(k.replace('f', ''))]: v \n", "                         for k, v in importance.items() if 'f' in k}\n", "            importance = [importance.get(f, 0) for f in feature_names]\n", "            title = f\"Feature Importance ({importance_type})\"\n", "        except:\n", "            print(\"Error getting XGBoost feature importance\")\n", "            return\n", "    \n", "    # For LightGBM\n", "    <PERSON><PERSON>(model, 'feature_importance'):\n", "        try:\n", "            importance = model.feature_importance(importance_type=importance_type)\n", "            title = f\"Feature Importance ({importance_type})\"\n", "        except:\n", "            print(\"Error getting LightGBM feature importance\")\n", "            return\n", "    \n", "    # If no importance found, return\n", "    if importance is None:\n", "        print(\"No feature importance available for this model type\")\n", "        return\n", "    \n", "    # Sort features by importance\n", "    indices = np.argsort(importance)\n", "    \n", "    # If many features, limit to top N\n", "    if len(feature_names) > max_features:\n", "        indices = indices[-max_features:]\n", "    \n", "    plt.figure(figsize=(12, 8))\n", "    plt.barh(range(len(indices)), [importance[i] for i in indices], align='center')\n", "    plt.yticks(range(len(indices)), [feature_names[i] for i in indices])\n", "    plt.title(title)\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"feature_importance.png\"), dpi=300)\n", "    plt.close()\n", "\n", "def plot_permutation_importance(model, X_test, y_test, feature_names, output_dir, \n", "                               n_repeats=10, random_state=42, max_features=15):\n", "    \"\"\"\n", "    Plot permutation importance for any model and save as PNG.\n", "    This is useful for any model type, including neural networks.\n", "    \"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    try:\n", "        # Calculate permutation importance\n", "        perm_importance = permutation_importance(model, X_test, y_test, \n", "                                              n_repeats=n_repeats, \n", "                                              random_state=random_state)\n", "        \n", "        # Get the mean importance and std\n", "        importances_mean = perm_importance.importances_mean\n", "        importances_std = perm_importance.importances_std\n", "        \n", "        # Sort features by importance\n", "        indices = np.argsort(importances_mean)\n", "        \n", "        # If many features, limit to top N\n", "        if len(feature_names) > max_features:\n", "            indices = indices[-max_features:]\n", "        \n", "        plt.figure(figsize=(12, 8))\n", "        plt.barh(range(len(indices)), \n", "                [importances_mean[i] for i in indices], \n", "                xerr=[importances_std[i] for i in indices],\n", "                align='center')\n", "        plt.yticks(range(len(indices)), [feature_names[i] for i in indices])\n", "        plt.title(\"Permutation Feature Importance\")\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, \"permutation_importance.png\"), dpi=300)\n", "        plt.close()\n", "    except Exception as e:\n", "        print(f\"Error calculating permutation importance: {e}\")\n", "\n", "def plot_classification_report(y_true, y_pred, classes, output_dir):\n", "    \"\"\"Plot classification report metrics as a heatmap and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    try:\n", "        report = classification_report(y_true, y_pred, output_dict=True)\n", "        \n", "        # Extract scores for each class\n", "        class_metrics = {cls: report[cls] for cls in classes if cls in report}\n", "        df_report = pd.DataFrame(class_metrics).T\n", "        \n", "        # Remove unnecessary columns\n", "        if 'support' in df_report.columns:\n", "            df_report = df_report.drop('support', axis=1)\n", "        \n", "        plt.figure(figsize=(10, 6))\n", "        sns.heatmap(df_report, annot=True, fmt='.2f', cmap='Blues')\n", "        plt.title('Classification Report')\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, \"classification_report.png\"), dpi=300)\n", "        plt.close()\n", "        \n", "        # Also save a version with support included\n", "        df_full_report = pd.DataFrame(report).T\n", "        df_full_report = df_full_report.drop(['accuracy', 'macro avg', 'weighted avg'], errors='ignore')\n", "        \n", "        plt.figure(figsize=(12, 8))\n", "        sns.heatmap(df_full_report, annot=True, fmt='.2f', cmap='Blues')\n", "        plt.title('Detailed Classification Report')\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, \"detailed_classification_report.png\"), dpi=300)\n", "        plt.close()\n", "    except Exception as e:\n", "        print(f\"Error plotting classification report: {e}\")\n", "\n", "def generate_model_visualizations(model, X_train, y_train, X_test, y_test, \n", "                                 feature_names, classes, training_history=None, \n", "                                 output_dir=\"model_visualizations\"):\n", "    \"\"\"\n", "    Generate all model evaluation visualizations.\n", "    \n", "    Parameters:\n", "    - model: Trained model\n", "    - X_train: Training features\n", "    - y_train: Training labels\n", "    - X_test: Test features\n", "    - y_test: Test labels\n", "    - feature_names: List of feature names\n", "    - classes: List of class names\n", "    - training_history: Training history object (for neural networks)\n", "    - output_dir: Directory to save visualization PNGs\n", "    \"\"\"\n", "    print(\"\\nGenerating model evaluation visualizations...\")\n", "    \n", "    # Set style\n", "    sns.set_style(\"whitegrid\")\n", "    plt.rcParams['font.size'] = 10\n", "    plt.rcParams['figure.figsize'] = (12, 8)\n", "    \n", "    # Create visualization directory\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    print(f\"Saving model visualizations to {viz_dir}\")\n", "    \n", "    # Get model predictions\n", "    try:\n", "        # For models with predict_proba\n", "        if hasattr(model, 'predict_proba'):\n", "            y_pred_proba = model.predict_proba(X_test)\n", "            y_pred = model.predict(X_test)\n", "        # For neural networks\n", "        else:\n", "            y_pred_proba = model.predict(X_test)\n", "            y_pred = np.argmax(y_pred_proba, axis=1)\n", "    except Exception as e:\n", "        print(f\"Error getting predictions: {e}\")\n", "        return\n", "    \n", "    # If labels are strings, convert to indices for neural networks\n", "    if hasattr(model, 'layers'):\n", "        label_map = {label: i for i, label in enumerate(classes)}\n", "        y_test_idx = np.array([label_map[label] for label in y_test])\n", "        y_pred_idx = y_pred\n", "        y_pred = np.array([classes[idx] for idx in y_pred])\n", "    else:\n", "        y_test_idx = y_test\n", "    \n", "    # Generate visualizations\n", "    \n", "    # 1. Learning curve (for neural networks)\n", "    if training_history is not None:\n", "        plot_learning_curve(training_history, output_dir)\n", "    \n", "    # 2. Confusion matrix\n", "    plot_confusion_matrix(y_test, y_pred, classes, output_dir)\n", "    \n", "    # 3. R<PERSON> curve\n", "    try:\n", "        plot_roc_curve(y_test_idx, y_pred_proba, classes, output_dir)\n", "    except Exception as e:\n", "        print(f\"Error plotting ROC curve: {e}\")\n", "    \n", "    # 4. Precision-<PERSON><PERSON><PERSON> curve\n", "    try:\n", "        plot_precision_recall_curve(y_test_idx, y_pred_proba, classes, output_dir)\n", "    except Exception as e:\n", "        print(f\"Error plotting Precision-Recall curve: {e}\")\n", "    \n", "    # 5. Feature importance for tree-based models\n", "    if hasattr(model, 'feature_importances_') or hasattr(model, 'get_booster') or hasattr(model, 'feature_importance'):\n", "        plot_feature_importance(model, feature_names, output_dir)\n", "    \n", "    # 6. Permutation importance (for any model)\n", "    plot_permutation_importance(model, X_test, y_test_idx, feature_names, output_dir)\n", "    \n", "    # 7. Classification report\n", "    plot_classification_report(y_test, y_pred, classes, output_dir)\n", "    \n", "    print(\"All model visualizations generated successfully!\")"]}, {"cell_type": "code", "execution_count": 5, "id": "6b5cba0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Starting data preprocessing pipeline...\n", "Successfully loaded 02-14-2018.csv (Memory: 63.42 MB)\n", "Successfully loaded 02-15-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-16-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-20-2018.csv (Memory: 667.09 MB)\n", "Successfully loaded 02-21-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-22-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-23-2018.csv (Memory: 88.00 MB)\n", "Successfully loaded 02-28-2018.csv (Memory: 51.45 MB)\n", "Successfully loaded 03-01-2018.csv (Memory: 27.79 MB)\n", "Successfully loaded 03-02-2018.csv (Memory: 88.00 MB)\n", "\n", "Processing 02-14-2018...\n", "Dropped 1 rows containing NaN values\n", "Computing DoS feature for 02-14-2018...\n", "Memory usage reduced from 96.84 MB to 65.13 MB (32.74% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Brute-force    380949\n", "Benign         374739\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Brute-force    380949\n", "Benign         374739\n", "Name: count, dtype: int64\n", "No balancing needed as malicious samples exceed or equal benign ones\n", "\n", "02-14-2018 dataset Information:\n", "Shape: (755688, 13)\n", "\n", "Label distribution:\n", "Label\n", "Brute-force    380949\n", "Benign         374739\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    380949\n", "Benign       374739\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 65.13129425048828 MB\n", "\n", "Processing 02-15-2018...\n", "Computing DoS feature for 02-15-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign        996077\n", "DoS attack     52498\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign        996077\n", "DoS attack     52498\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign        52498\n", "DoS attack    52498\n", "Name: count, dtype: int64\n", "\n", "02-15-2018 dataset Information:\n", "Shape: (104996, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign        52498\n", "DoS attack    52498\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       52498\n", "Malicious    52498\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 6.8089752197265625 MB\n", "\n", "Processing 02-16-2018...\n", "Computing DoS feature for 02-16-2018...\n", "Memory usage reduced from 144.25 MB to 132.25 MB (8.32% reduction)\n", "Label distribution after transformation:\n", "Label\n", "DoS attack    601802\n", "Benign        446772\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "DoS attack    601802\n", "Benign        446772\n", "Name: count, dtype: int64\n", "No balancing needed as malicious samples exceed or equal benign ones\n", "\n", "02-16-2018 dataset Information:\n", "Shape: (1048574, 13)\n", "\n", "Label distribution:\n", "Label\n", "DoS attack    601802\n", "Benign        446772\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    601802\n", "Benign       446772\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 132.24984741210938 MB\n", "\n", "Processing 02-20-2018...\n", "Computing DoS feature for 02-20-2018...\n", "Memory usage reduced from 1107.02 MB to 773.48 MB (30.13% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign         7372557\n", "DDoS attack     576191\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign         7372557\n", "DDoS attack     576191\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign         576191\n", "DDoS attack    576191\n", "Name: count, dtype: int64\n", "\n", "02-20-2018 dataset Information:\n", "Shape: (1152382, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign         576191\n", "DDoS attack    576191\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       576191\n", "Malicious    576191\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 74.73180389404297 MB\n", "\n", "Processing 02-21-2018...\n", "Computing DoS feature for 02-21-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "DDoS attack    687742\n", "Benign         360833\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "DDoS attack    687742\n", "Benign         360833\n", "Name: count, dtype: int64\n", "No balancing needed as malicious samples exceed or equal benign ones\n", "\n", "02-21-2018 dataset Information:\n", "Shape: (1048575, 13)\n", "\n", "Label distribution:\n", "Label\n", "DDoS attack    687742\n", "Benign         360833\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    687742\n", "Benign       360833\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 100.24997329711914 MB\n", "\n", "Processing 02-22-2018...\n", "Computing DoS feature for 02-22-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign        1048213\n", "Web attack        362\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign        1048213\n", "Web attack        362\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign        362\n", "Web attack    362\n", "Name: count, dtype: int64\n", "\n", "02-22-2018 dataset Information:\n", "Shape: (724, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign        362\n", "Web attack    362\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       362\n", "Malicious    362\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 0.0469512939453125 MB\n", "\n", "Processing 02-23-2018...\n", "Computing DoS feature for 02-23-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign        1048009\n", "Web attack        566\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign        1048009\n", "Web attack        566\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign        566\n", "Web attack    566\n", "Name: count, dtype: int64\n", "\n", "02-23-2018 dataset Information:\n", "Shape: (1132, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign        566\n", "Web attack    566\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       566\n", "Malicious    566\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 0.0734100341796875 MB\n", "\n", "Processing 02-28-2018...\n", "Computing DoS feature for 02-28-2018...\n", "Memory usage reduced from 81.61 MB to 74.59 MB (8.60% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign           544200\n", "Infilteration     68871\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign           544200\n", "Infilteration     68871\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign           68871\n", "Infilteration    68871\n", "Name: count, dtype: int64\n", "\n", "02-28-2018 dataset Information:\n", "Shape: (137742, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign           68871\n", "Infilteration    68871\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       68871\n", "Malicious    68871\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 13.136100769042969 MB\n", "\n", "Processing 03-01-2018...\n", "Computing DoS feature for 03-01-2018...\n", "Memory usage reduced from 43.43 MB to 39.64 MB (8.73% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign           238037\n", "Infilteration     93063\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign           238037\n", "Infilteration     93063\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign           93063\n", "Infilteration    93063\n", "Name: count, dtype: int64\n", "\n", "03-01-2018 dataset Information:\n", "Shape: (186126, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign           93063\n", "Infilteration    93063\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       93063\n", "Malicious    93063\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 17.75035858154297 MB\n", "\n", "Processing 03-02-2018...\n", "Computing DoS feature for 03-02-2018...\n", "Memory usage reduced from 144.25 MB to 100.25 MB (30.50% reduction)\n", "Label distribution after transformation:\n", "Label\n", "Benign    762384\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "\n", "Balancing individual file if needed...\n", "Initial class distribution:\n", "Label\n", "Benign    762384\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "Balancing dataset as benign samples exceed malicious ones...\n", "Class distribution after balancing:\n", "Label\n", "Benign    286191\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "\n", "03-02-2018 dataset Information:\n", "Shape: (572382, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign    286191\n", "Botnet    286191\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Benign       286191\n", "Malicious    286191\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 37.11888885498047 MB\n", "\n", "Combining all datasets...\n", "\n", "Combined dataset Information:\n", "Shape: (5008321, 13)\n", "\n", "Label distribution:\n", "Label\n", "Benign           2260086\n", "DDoS attack      1263933\n", "DoS attack        654300\n", "Brute-force       380949\n", "Botnet            286191\n", "Infilteration     161934\n", "Web attack           928\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    2748235\n", "Benign       2260086\n", "Name: count, dtype: int64\n", "\n", "Number of features: 11\n", "\n", "Memory usage: 439.4203796386719 MB\n", "\n", "Handling outliers...\n", "Processing outliers using winsorize strategy...\n", "\n", "Selecting top 10 features...\n", "Selected features: ['Conn_<PERSON>', 'Fwd Seg Size Min', 'Bwd Pkt Len Mean', 'Dst Port', 'TotLen Fwd Pkts', 'Init Fwd Win Byts', 'Flow Duration', 'Flow IAT Mean', 'Init Bwd Win Byts', 'Flow IAT Max', 'Label', 'Threat']\n", "\n", "Removing unwanted labels and balancing dataset...\n", "Removed 162862 rows with labels: ['Infilteration', 'Web attack']\n", "Class distribution after balancing:\n", "Label\n", "Benign         286191\n", "Botnet         286191\n", "Brute-force    286191\n", "DDoS attack    286191\n", "DoS attack     286191\n", "Name: count, dtype: int64\n", "\n", "Preparing data for modeling...\n", "Using RobustScaler for scaling...\n", "\n", "Final dataset preparation complete!\n", "\n", "Training dataset Information:\n", "Shape: (1144764, 12)\n", "\n", "Label distribution:\n", "Label\n", "DoS attack     228953\n", "Benign         228953\n", "Botnet         228953\n", "Brute-force    228953\n", "DDoS attack    228952\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    915811\n", "Benign       228953\n", "Name: count, dtype: int64\n", "\n", "Number of features: 10\n", "\n", "Memory usage: 113.54013061523438 MB\n", "\n", "Testing dataset Information:\n", "Shape: (286191, 12)\n", "\n", "Label distribution:\n", "Label\n", "DDoS attack    57239\n", "Benign         57238\n", "Brute-force    57238\n", "DoS attack     57238\n", "Botnet         57238\n", "Name: count, dtype: int64\n", "\n", "Threat distribution:\n", "Threat\n", "Malicious    228953\n", "Benign        57238\n", "Name: count, dtype: int64\n", "\n", "Number of features: 10\n", "\n", "Memory usage: 28.385032653808594 MB\n", "\n", "Class weights: {'Benign': np.float64(0.9999991264582687), 'Botnet': np.float64(0.9999991264582687), 'Brute-force': np.float64(0.9999991264582687), 'DDoS attack': np.float64(1.0000034941821867), 'DoS attack': np.float64(0.9999991264582687)}\n", "All artifacts saved to .\\artifacts\n", "\n", "Note: Use StratifiedKFold during modeling for robust validation\n", "Label distribution after transformation:\n", "Label\n", "Brute-force    5025\n", "Benign         4975\n", "Name: count, dtype: int64\n", "\n", "Generating visualizations...\n", "Saving visualizations to .\\artifacts\\visualizations\n", "Error plotting Flow IAT Mean: Unable to allocate 4.76 GiB for an array with shape (1, 639287740) and data type int64\n", "Error plotting Flow IAT Max: Unable to allocate 899. MiB for an array with shape (117892340,) and data type float64\n", "\n", "Error occurred during preprocessing: Unalignable boolean Series provided as indexer (index of the boolean Series and of the indexed object do not match).\n", "\n", "Fatal error in preprocessing pipeline: Unalignable boolean Series provided as indexer (index of the boolean Series and of the indexed object do not match).\n"]}, {"ename": "IndexingError", "evalue": "Unalignable boolean Series provided as indexer (index of the boolean Series and of the indexed object do not match).", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mIndexingError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 826\u001b[39m\n\u001b[32m    824\u001b[39m np.random.seed(\u001b[32m42\u001b[39m)\n\u001b[32m    825\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m826\u001b[39m     train_df, test_df, class_weights, scaler = \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    827\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbase_path\u001b[49m\u001b[43m=\u001b[49m\u001b[43mBASE_PATH\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    828\u001b[39m \u001b[43m        \u001b[49m\u001b[43moutput_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mOUTPUT_DIR\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    829\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdates\u001b[49m\u001b[43m=\u001b[49m\u001b[43mDATES\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    830\u001b[39m \u001b[43m        \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m100000\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    831\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtest_size\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.2\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    832\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrandom_state\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m42\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    833\u001b[39m \u001b[43m        \u001b[49m\u001b[43moutlier_strategy\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mwinsorize\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    834\u001b[39m \u001b[43m        \u001b[49m\u001b[43moutlier_threshold\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m3.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    835\u001b[39m \u001b[43m        \u001b[49m\u001b[43muse_robust_scaling\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    836\u001b[39m \u001b[43m        \u001b[49m\u001b[43mgenerate_viz\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Enable visualization generation\u001b[39;49;00m\n\u001b[32m    837\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    838\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mPreprocessing pipeline completed successfully!\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    839\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 793\u001b[39m, in \u001b[36mmain\u001b[39m\u001b[34m(base_path, output_dir, dates, chunksize, test_size, random_state, outlier_strategy, outlier_threshold, use_robust_scaling, generate_viz)\u001b[39m\n\u001b[32m    790\u001b[39m                 original_sample_processed = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    792\u001b[39m         \u001b[38;5;66;03m# Generate all visualizations\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m793\u001b[39m         \u001b[43mgenerate_visualizations\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    794\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtrain_df\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtrain_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    795\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtest_df\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtest_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    796\u001b[39m \u001b[43m            \u001b[49m\u001b[43mtop_features\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtop_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    797\u001b[39m \u001b[43m            \u001b[49m\u001b[43moutput_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43moutput_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    798\u001b[39m \u001b[43m            \u001b[49m\u001b[43moriginal_df\u001b[49m\u001b[43m=\u001b[49m\u001b[43moriginal_sample_processed\u001b[49m\n\u001b[32m    799\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    801\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m train_df, test_df, class_weights, scaler\n\u001b[32m    802\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 282\u001b[39m, in \u001b[36mgenerate_visualizations\u001b[39m\u001b[34m(train_df, test_df, top_features, output_dir, original_df)\u001b[39m\n\u001b[32m    280\u001b[39m plot_correlation_matrix(train_df, top_features, output_dir, stage=\u001b[33m\"\u001b[39m\u001b[33mtrain\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    281\u001b[39m plot_feature_by_class(train_df, top_features, output_dir)\n\u001b[32m--> \u001b[39m\u001b[32m282\u001b[39m \u001b[43mplot_pca_visualization\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrain_df\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_features\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moutput_dir\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstage\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtrain\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    284\u001b[39m \u001b[38;5;66;03m# Visualizations for test data\u001b[39;00m\n\u001b[32m    285\u001b[39m plot_class_distributions(test_df, output_dir, stage=\u001b[33m\"\u001b[39m\u001b[33mtest\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 142\u001b[39m, in \u001b[36mplot_pca_visualization\u001b[39m\u001b[34m(df, top_features, output_dir, stage)\u001b[39m\n\u001b[32m    140\u001b[39m top_classes = df[\u001b[33m\"\u001b[39m\u001b[33mLabel\u001b[39m\u001b[33m\"\u001b[39m].value_counts().nlargest(\u001b[32m5\u001b[39m).index.tolist()\n\u001b[32m    141\u001b[39m mask = df[\u001b[33m\"\u001b[39m\u001b[33mLabel\u001b[39m\u001b[33m\"\u001b[39m].isin(top_classes)\n\u001b[32m--> \u001b[39m\u001b[32m142\u001b[39m pca_df_limited = \u001b[43mpca_df\u001b[49m\u001b[43m.\u001b[49m\u001b[43mloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43mmask\u001b[49m\u001b[43m]\u001b[49m.copy()\n\u001b[32m    143\u001b[39m pca_df_limited[\u001b[33m\"\u001b[39m\u001b[33mLabel\u001b[39m\u001b[33m\"\u001b[39m] = df.loc[mask, \u001b[33m\"\u001b[39m\u001b[33mLabel\u001b[39m\u001b[33m\"\u001b[39m].values\n\u001b[32m    145\u001b[39m plt.figure(figsize=(\u001b[32m12\u001b[39m, \u001b[32m10\u001b[39m))\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Desktop\\projects\\big data\\pre-processing-10\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py:1191\u001b[39m, in \u001b[36m_LocationIndexer.__getitem__\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   1189\u001b[39m maybe_callable = com.apply_if_callable(key, \u001b[38;5;28mself\u001b[39m.obj)\n\u001b[32m   1190\u001b[39m maybe_callable = \u001b[38;5;28mself\u001b[39m._check_deprecated_callable_usage(key, maybe_callable)\n\u001b[32m-> \u001b[39m\u001b[32m1191\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_getitem_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmaybe_callable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m=\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Desktop\\projects\\big data\\pre-processing-10\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py:1413\u001b[39m, in \u001b[36m_LocIndexer._getitem_axis\u001b[39m\u001b[34m(self, key, axis)\u001b[39m\n\u001b[32m   1411\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._get_slice_axis(key, axis=axis)\n\u001b[32m   1412\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m com.is_bool_indexer(key):\n\u001b[32m-> \u001b[39m\u001b[32m1413\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_getbool_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m=\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1414\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m is_list_like_indexer(key):\n\u001b[32m   1415\u001b[39m     \u001b[38;5;66;03m# an iterable multi-selection\u001b[39;00m\n\u001b[32m   1416\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28misinstance\u001b[39m(key, \u001b[38;5;28mtuple\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(labels, MultiIndex)):\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Desktop\\projects\\big data\\pre-processing-10\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py:1209\u001b[39m, in \u001b[36m_LocationIndexer._getbool_axis\u001b[39m\u001b[34m(self, key, axis)\u001b[39m\n\u001b[32m   1205\u001b[39m \u001b[38;5;129m@final\u001b[39m\n\u001b[32m   1206\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_getbool_axis\u001b[39m(\u001b[38;5;28mself\u001b[39m, key, axis: AxisInt):\n\u001b[32m   1207\u001b[39m     \u001b[38;5;66;03m# caller is responsible for ensuring non-None axis\u001b[39;00m\n\u001b[32m   1208\u001b[39m     labels = \u001b[38;5;28mself\u001b[39m.obj._get_axis(axis)\n\u001b[32m-> \u001b[39m\u001b[32m1209\u001b[39m     key = \u001b[43mcheck_bool_indexer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1210\u001b[39m     inds = key.nonzero()[\u001b[32m0\u001b[39m]\n\u001b[32m   1211\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.obj._take_with_is_copy(inds, axis=axis)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Desktop\\projects\\big data\\pre-processing-10\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py:2662\u001b[39m, in \u001b[36mcheck_bool_indexer\u001b[39m\u001b[34m(index, key)\u001b[39m\n\u001b[32m   2660\u001b[39m indexer = result.index.get_indexer_for(index)\n\u001b[32m   2661\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m -\u001b[32m1\u001b[39m \u001b[38;5;129;01min\u001b[39;00m indexer:\n\u001b[32m-> \u001b[39m\u001b[32m2662\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m IndexingError(\n\u001b[32m   2663\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mUnalignable boolean Series provided as \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   2664\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mindexer (index of the boolean Series and of \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   2665\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mthe indexed object do not match).\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   2666\u001b[39m     )\n\u001b[32m   2668\u001b[39m result = result.take(indexer)\n\u001b[32m   2670\u001b[39m \u001b[38;5;66;03m# fall through for boolean\u001b[39;00m\n", "\u001b[31mIndexingError\u001b[39m: Unalignable boolean Series provided as indexer (index of the boolean Series and of the indexed object do not match)."]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import warnings\n", "import os\n", "from imblearn.under_sampling import RandomUnderSampler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import MinMaxScaler, RobustScaler\n", "from sklearn.utils import class_weight\n", "import joblib\n", "import json\n", "from typing import Dict, Tu<PERSON>, Any\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.decomposition import PCA\n", "\n", "# Suppress warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "### Visualization Functions\n", "\n", "def create_visualization_dir(output_dir):\n", "    \"\"\"Create a directory for storing visualizations.\"\"\"\n", "    viz_dir = os.path.join(output_dir, \"visualizations\")\n", "    os.makedirs(viz_dir, exist_ok=True)\n", "    return viz_dir\n", "\n", "def plot_class_distributions(df, output_dir, stage=\"original\"):\n", "    \"\"\"Plot distribution of classes and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    label_counts = df[\"Label\"].value_counts()\n", "    ax = sns.barplot(x=label_counts.index, y=label_counts.values)\n", "    plt.title(f\"Distribution of Classes ({stage})\")\n", "    plt.xlabel(\"Class\")\n", "    plt.ylabel(\"Count\")\n", "    plt.xticks(rotation=45, ha=\"right\")\n", "    plt.tight_layout()\n", "    \n", "    # Add count labels on bars\n", "    for i, count in enumerate(label_counts.values):\n", "        ax.text(i, count + (count * 0.01), f\"{count:,}\", ha='center')\n", "    \n", "    plt.savefig(os.path.join(viz_dir, f\"class_distribution_{stage}.png\"), dpi=300)\n", "    plt.close()\n", "    \n", "    if \"Threat\" in df.columns:\n", "        plt.figure(figsize=(10, 6))\n", "        threat_counts = df[\"Threat\"].value_counts()\n", "        ax = sns.barplot(x=threat_counts.index, y=threat_counts.values)\n", "        plt.title(f\"Binary Class Distribution ({stage})\")\n", "        plt.xlabel(\"Threat Type\")\n", "        plt.ylabel(\"Count\")\n", "        \n", "        # Add count labels on bars\n", "        for i, count in enumerate(threat_counts.values):\n", "            ax.text(i, count + (count * 0.01), f\"{count:,}\", ha='center')\n", "        \n", "        plt.savefig(os.path.join(viz_dir, f\"binary_class_distribution_{stage}.png\"), dpi=300)\n", "        plt.close()\n", "\n", "def plot_feature_distributions(df, top_features, output_dir, stage=\"original\"):\n", "    \"\"\"Plot distributions of top features and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    for feature in top_features:\n", "        if feature in df.columns:\n", "            plt.figure(figsize=(10, 6))\n", "            plt.title(f\"{feature} Distribution ({stage})\")\n", "            \n", "            try:\n", "                # Check if the feature has too many unique values\n", "                if df[feature].nunique() < 10:\n", "                    sns.countplot(data=df, x=feature)\n", "                    plt.xlabel(feature)\n", "                    plt.ylabel(\"Count\")\n", "                else:\n", "                    sns.histplot(data=df, x=feature, kde=True)\n", "                    plt.xlabel(feature)\n", "                    plt.ylabel(\"Frequency\")\n", "                \n", "                plt.tight_layout()\n", "                plt.savefig(os.path.join(viz_dir, f\"{feature.replace(' ', '_')}_distribution_{stage}.png\"), dpi=300)\n", "            except Exception as e:\n", "                print(f\"Error plotting {feature}: {str(e)}\")\n", "            finally:\n", "                plt.close()\n", "\n", "def plot_correlation_matrix(df, top_features, output_dir, stage=\"original\"):\n", "    \"\"\"Plot correlation matrix between features and save as PNG.\"\"\"\n", "    if not all(feature in df.columns for feature in top_features):\n", "        print(\"Not all top features present, skipping correlation matrix\")\n", "        return\n", "    \n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    plt.figure(figsize=(12, 10))\n", "    corr = df[top_features].corr()\n", "    mask = np.triu(np.ones_like(corr, dtype=bool))\n", "    sns.heatmap(corr, mask=mask, annot=True, fmt=\".2f\", cmap=\"coolwarm\", \n", "                vmin=-1, vmax=1, square=True, linewidths=.5)\n", "    plt.title(f\"Feature Correlation Matrix ({stage})\")\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, f\"correlation_matrix_{stage}.png\"), dpi=300)\n", "    plt.close()\n", "\n", "def plot_pca_visualization(df, top_features, output_dir, stage=\"scaled\"):\n", "    \"\"\"Create PCA visualization of the feature space and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    if not all(feature in df.columns for feature in top_features):\n", "        print(\"Not all top features present, skipping PCA visualization\")\n", "        return\n", "    \n", "    # Apply PCA to reduce to 2 dimensions\n", "    pca = PCA(n_components=2)\n", "    pca_result = pca.fit_transform(df[top_features])\n", "    \n", "    # Create a dataframe with PCA results\n", "    pca_df = pd.DataFrame({\n", "        'PCA1': pca_result[:, 0],\n", "        'PCA2': pca_result[:, 1]\n", "    })\n", "    \n", "    # Add class information if available\n", "    if \"Threat\" in df.columns:\n", "        pca_df[\"Threat\"] = df[\"Threat\"].values\n", "        \n", "        plt.figure(figsize=(12, 10))\n", "        sns.scatterplot(data=pca_df, x='PCA1', y='PCA2', hue='Threat', alpha=0.7)\n", "        plt.title(f\"PCA of Features by Threat Type ({stage})\")\n", "        plt.xlabel(f\"PCA1 ({pca.explained_variance_ratio_[0]:.2%} variance)\")\n", "        plt.ylabel(f\"PCA2 ({pca.explained_variance_ratio_[1]:.2%} variance)\")\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, f\"pca_by_threat_{stage}.png\"), dpi=300)\n", "        plt.close()\n", "    \n", "    if \"Label\" in df.columns:\n", "        # Limit to top 5 classes for clarity\n", "        top_classes = df[\"Label\"].value_counts().nlargest(5).index.tolist()\n", "        mask = df[\"Label\"].isin(top_classes)\n", "        pca_df_limited = pca_df.loc[mask].copy()\n", "        pca_df_limited[\"Label\"] = df.loc[mask, \"Label\"].values\n", "        \n", "        plt.figure(figsize=(12, 10))\n", "        sns.scatterplot(data=pca_df_limited, x='PCA1', y='PCA2', hue='Label', alpha=0.7)\n", "        plt.title(f\"PCA of Features by Attack Type ({stage})\")\n", "        plt.xlabel(f\"PCA1 ({pca.explained_variance_ratio_[0]:.2%} variance)\")\n", "        plt.ylabel(f\"PCA2 ({pca.explained_variance_ratio_[1]:.2%} variance)\")\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, f\"pca_by_label_{stage}.png\"), dpi=300)\n", "        plt.close()\n", "\n", "def plot_feature_by_class(df, top_features, output_dir, n_features=5):\n", "    \"\"\"Plot feature distributions by class and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    showcase_features = top_features[:n_features] if len(top_features) > n_features else top_features\n", "    if not all(feature in df.columns for feature in showcase_features):\n", "        print(\"Not all showcase features present, skipping feature by class plots\")\n", "        return\n", "    \n", "    # Feature distributions by binary class\n", "    if \"Threat\" in df.columns:\n", "        for feature in showcase_features:\n", "            plt.figure(figsize=(10, 6))\n", "            sns.histplot(data=df, x=feature, hue=\"Threat\", kde=True, element=\"step\")\n", "            plt.title(f\"{feature} by Threat Type\")\n", "            plt.tight_layout()\n", "            plt.savefig(os.path.join(viz_dir, f\"{feature.replace(' ', '_')}_by_threat.png\"), dpi=300)\n", "            plt.close()\n", "    \n", "    # Feature distributions by detailed class (limit to top 5 classes by count if many exist)\n", "    if \"Label\" in df.columns:\n", "        top_classes = df[\"Label\"].value_counts().nlargest(5).index.tolist()\n", "        class_df = df[df[\"Label\"].isin(top_classes)]\n", "        \n", "        for feature in showcase_features:\n", "            plt.figure(figsize=(12, 7))\n", "            sns.boxplot(data=class_df, x=\"Label\", y=feature)\n", "            plt.title(f\"{feature} by Attack Type\")\n", "            plt.xticks(rotation=45, ha=\"right\")\n", "            plt.tight_layout()\n", "            plt.savefig(os.path.join(viz_dir, f\"{feature.replace(' ', '_')}_by_label_boxplot.png\"), dpi=300)\n", "            plt.close()\n", "\n", "def plot_outlier_detection(df, top_features, output_dir, threshold=3.0):\n", "    \"\"\"Plot outlier detection visualizations and save as PNG.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    # Z-score outlier visualization (up to 5 features for clarity)\n", "    showcase_features = top_features[:5] if len(top_features) > 5 else top_features\n", "    if not all(feature in df.columns for feature in showcase_features):\n", "        print(\"Not all showcase features present, skipping outlier detection\")\n", "        return\n", "    \n", "    plt.figure(figsize=(15, 10))\n", "    for i, feature in enumerate(showcase_features, 1):\n", "        plt.subplot(len(showcase_features), 1, i)\n", "        z_scores = np.abs((df[feature] - df[feature].mean()) / df[feature].std())\n", "        sns.scatterplot(x=df.index, y=df[feature], hue=z_scores > threshold)\n", "        plt.title(f\"Outlier Detection: {feature}\")\n", "        plt.xlabel(\"Index\")\n", "        plt.ylabel(feature)\n", "        plt.legend([\"Normal\", \"Outlier\"], loc=\"upper right\")\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"outlier_detection.png\"), dpi=300)\n", "    plt.close()\n", "    \n", "    # Box plots for outlier visualization\n", "    plt.figure(figsize=(15, 10))\n", "    for i, feature in enumerate(showcase_features, 1):\n", "        plt.subplot(len(showcase_features), 1, i)\n", "        sns.boxplot(x=df[feature])\n", "        plt.title(f\"Box Plot: {feature}\")\n", "        plt.xlabel(feature)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"feature_boxplots.png\"), dpi=300)\n", "    plt.close()\n", "\n", "def plot_train_test_distributions(train_df, test_df, output_dir):\n", "    \"\"\"Plot distribution comparison between train and test sets.\"\"\"\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    \n", "    # Compare label distributions\n", "    train_labels = train_df[\"Label\"].value_counts().rename(\"Train\")\n", "    test_labels = test_df[\"Label\"].value_counts().rename(\"Test\")\n", "    label_comparison = pd.DataFrame([train_labels, test_labels]).T.fillna(0)\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    label_comparison.plot(kind=\"bar\")\n", "    plt.title(\"Label Distribution Comparison: Train vs Test\")\n", "    plt.xlabel(\"Label\")\n", "    plt.ylabel(\"Count\")\n", "    plt.xticks(rotation=45, ha=\"right\")\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(viz_dir, \"train_test_label_distribution.png\"), dpi=300)\n", "    plt.close()\n", "    \n", "    # Compare threat distributions\n", "    if \"Threat\" in train_df.columns and \"Threat\" in test_df.columns:\n", "        train_threat = train_df[\"Threat\"].value_counts().rename(\"Train\")\n", "        test_threat = test_df[\"Threat\"].value_counts().rename(\"Test\")\n", "        threat_comparison = pd.DataFrame([train_threat, test_threat]).T.fillna(0)\n", "        \n", "        plt.figure(figsize=(10, 6))\n", "        threat_comparison.plot(kind=\"bar\")\n", "        plt.title(\"Threat Distribution Comparison: Train vs Test\")\n", "        plt.xlabel(\"Threat\")\n", "        plt.ylabel(\"Count\")\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(viz_dir, \"train_test_threat_distribution.png\"), dpi=300)\n", "        plt.close()\n", "\n", "def generate_visualizations(train_df, test_df, top_features, output_dir, original_df=None):\n", "    \"\"\"Generate and save all visualizations.\"\"\"\n", "    print(\"\\nGenerating visualizations...\")\n", "    \n", "    # Set style\n", "    sns.set_style(\"whitegrid\")\n", "    plt.rcParams['font.size'] = 10\n", "    plt.rcParams['figure.figsize'] = (12, 8)\n", "    \n", "    # Create visualizations directory\n", "    viz_dir = create_visualization_dir(output_dir)\n", "    print(f\"Saving visualizations to {viz_dir}\")\n", "    \n", "    # If original dataframe is provided, create visualizations of pre-processed data\n", "    if original_df is not None:\n", "        plot_class_distributions(original_df, output_dir, stage=\"original\")\n", "        plot_feature_distributions(original_df, top_features, output_dir, stage=\"original\")\n", "        plot_correlation_matrix(original_df, top_features, output_dir, stage=\"original\")\n", "        plot_outlier_detection(original_df, top_features, output_dir)\n", "    \n", "    # Visualizations for training data\n", "    plot_class_distributions(train_df, output_dir, stage=\"train\")\n", "    plot_feature_distributions(train_df, top_features, output_dir, stage=\"train\")\n", "    plot_correlation_matrix(train_df, top_features, output_dir, stage=\"train\")\n", "    plot_feature_by_class(train_df, top_features, output_dir)\n", "    plot_pca_visualization(train_df, top_features, output_dir, stage=\"train\")\n", "    \n", "    # Visualizations for test data\n", "    plot_class_distributions(test_df, output_dir, stage=\"test\")\n", "    \n", "    # Compare train and test distributions\n", "    plot_train_test_distributions(train_df, test_df, output_dir)\n", "    \n", "    print(\"All visualizations generated successfully!\")\n", "\n", "### Data Loading and Initial Preprocessing Functions\n", "\n", "def load_csv_files(\n", "    base_path: str, date_list: list, chunksize: int = None\n", ") -> Dict[str, pd.DataFrame]:\n", "    \"\"\"\n", "    Load CSV files using only the necessary columns.\n", "\n", "    Required columns include:\n", "      - For computing features: 'Timestamp', 'Dst Port', 'Flow Duration'\n", "      - For final features: 'Fwd Seg Size Min', 'Bwd Pkt Len Mean', 'TotLen Fwd Pkts',\n", "        'Init Fwd Win Byts', 'Flow IAT Mean', 'Init Bwd Win Byts', 'Flow IAT Max'\n", "      - For target: 'Label'\n", "    \"\"\"\n", "    required_cols = [\n", "        \"Timestamp\",\n", "        \"Dst Port\",\n", "        \"Flow Duration\",\n", "        \"Fwd Seg Size Min\",\n", "        \"Bwd Pkt Len Mean\",\n", "        \"TotLen Fwd Pkts\",\n", "        \"Init Fwd Win Byts\",\n", "        \"Flow IAT Mean\",\n", "        \"Init Bwd Win Byts\",\n", "        \"Flow IAT Max\",\n", "        \"Label\",\n", "    ]\n", "\n", "    dataframes = {}\n", "    for date in date_list:\n", "        file_path = os.path.join(base_path, f\"{date}.csv\")\n", "        try:\n", "            if chunksize:\n", "                chunks = []\n", "                for chunk in pd.read_csv(\n", "                    file_path,\n", "                    usecols=required_cols,\n", "                    chunksize=chunksize,\n", "                    low_memory=False,\n", "                ):\n", "                    chunks.append(chunk)\n", "                dataframes[date] = pd.concat(chunks, ignore_index=True)\n", "            else:\n", "                dataframes[date] = pd.read_csv(\n", "                    file_path, usecols=required_cols, low_memory=False\n", "                )\n", "            mem_usage = dataframes[date].memory_usage().sum() / 1024**2\n", "            print(f\"Successfully loaded {date}.csv (Memory: {mem_usage:.2f} MB)\")\n", "        except Exception as e:\n", "            print(f\"Error loading {date}.csv: {str(e)}\")\n", "    return dataframes\n", "\n", "\n", "def preprocess_initial_columns(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Remove any columns that are not necessary.\n", "    (Since we already load only the required columns, this step is minimal.)\n", "    \"\"\"\n", "    return df\n", "\n", "\n", "def clean_infinite_values(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Replace infinite values with NaN and drop rows containing NaNs.\"\"\"\n", "    df = df.replace([\"Infinity\", \"infinity\"], np.inf)\n", "    df = df.replace([np.inf, -np.inf], np.nan)\n", "    initial_rows = len(df)\n", "    df.dropna(inplace=True)\n", "    dropped_rows = initial_rows - len(df)\n", "    if dropped_rows > 0:\n", "        print(f\"Dropped {dropped_rows} rows containing NaN values\")\n", "    return df\n", "\n", "\n", "def drop_timestamp(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Remove the Timestamp column once it's no longer needed.\"\"\"\n", "    if \"Timestamp\" in df.columns:\n", "        df.drop(columns=\"Timestamp\", inplace=True)\n", "    return df\n", "\n", "\n", "### Label Generation and Memory Optimization\n", "\n", "\n", "def generate_binary_label(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Generate binary classification labels.\"\"\"\n", "    df[\"Threat\"] = df[\"Label\"].apply(\n", "        lambda x: \"Benign\" if x == \"Benign\" else \"Malicious\"\n", "    )\n", "    return df\n", "\n", "\n", "def optimize_memory_usage(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Convert numerical columns to float32 to optimize memory usage.\"\"\"\n", "    start_mem = df.memory_usage().sum() / 1024**2\n", "    for col in df.columns:\n", "        if df[col].dtype != object:\n", "            if not np.isfinite(df[col]).all():\n", "                df[col] = df[col].fillna(df[col].min() if not df[col].empty else 0)\n", "            df[col] = df[col].astype(np.float32)\n", "    end_mem = df.memory_usage().sum() / 1024**2\n", "    reduction = 100 * (start_mem - end_mem) / start_mem\n", "    print(\n", "        f\"Memory usage reduced from {start_mem:.2f} MB to {end_mem:.2f} MB ({reduction:.2f}% reduction)\"\n", "    )\n", "    return df\n", "\n", "\n", "def transform_multi_label(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Simplify the label categories.\"\"\"\n", "    mapping = {\n", "        \"SSH-Bruteforce\": \"Brute-force\",\n", "        \"FTP-BruteForce\": \"Brute-force\",\n", "        \"Brute Force -XSS\": \"Web attack\",\n", "        \"Brute Force -Web\": \"Web attack\",\n", "        \"SQL Injection\": \"Web attack\",\n", "        \"DoS attacks-Hulk\": \"DoS attack\",\n", "        \"DoS attacks-SlowHTTPTest\": \"DoS attack\",\n", "        \"DoS attacks-Slowloris\": \"DoS attack\",\n", "        \"DoS attacks-GoldenEye\": \"DoS attack\",\n", "        \"DDOS attack-HOIC\": \"DDoS attack\",\n", "        \"DDOS attack-LOIC-UDP\": \"DDoS attack\",\n", "        \"DDoS attacks-LOIC-HTTP\": \"DDoS attack\",\n", "        \"Bot\": \"Botnet\",\n", "        \"Infilteration\": \"Infilteration\",\n", "        \"Benign\": \"Benign\",\n", "        \"Label\": \"Benign\",\n", "    }\n", "    df[\"Label\"] = df[\"Label\"].map(mapping).fillna(\"Other\")\n", "    print(\"Label distribution after transformation:\")\n", "    print(df[\"Label\"].value_counts())\n", "    return df\n", "\n", "\n", "### Balancing Function\n", "\n", "\n", "def balance_dataset_if_needed(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Balance the dataset if benign samples exceed malicious ones.\n", "    (Uses random under-sampling.)\n", "    \"\"\"\n", "    label_counts = df[\"Label\"].value_counts()\n", "    print(\"Initial class distribution:\")\n", "    print(label_counts)\n", "    benign_count = label_counts.get(\"Benign\", 0)\n", "    malicious_count = label_counts.drop(\"Benign\", errors=\"ignore\").sum()\n", "    if benign_count > malicious_count:\n", "        print(\"Balancing dataset as benign samples exceed malicious ones...\")\n", "        X = df.drop([\"Label\", \"Threat\"], axis=1)\n", "        y = df[\"Label\"]\n", "        rus = RandomUnderSampler(random_state=42)\n", "        X_balanced, y_balanced = rus.fit_resample(X, y)\n", "        df = pd.concat([X_balanced, pd.Series(y_balanced, name=\"Label\")], axis=1)\n", "        df = generate_binary_label(df)\n", "        print(\"Class distribution after balancing:\")\n", "        print(df[\"Label\"].value_counts())\n", "    else:\n", "        print(\"No balancing needed as malicious samples exceed or equal benign ones\")\n", "    return df\n", "\n", "\n", "### DoS-Specific Feature Engineering\n", "\n", "\n", "def add_dos_features(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Compute the 'Conn_Rate' feature needed for the final top features.\n", "    Uses 'Timestamp', 'Dst Port', and 'Flow Duration'.\n", "\n", "    To avoid errors, we first convert 'Flow Duration' to numeric.\n", "    \"\"\"\n", "    df[\"Timestamp\"] = pd.to_datetime(df[\"Timestamp\"], errors=\"coerce\")\n", "    df[\"Flow Duration\"] = pd.to_numeric(df[\"Flow Duration\"], errors=\"coerce\")\n", "    df = df.dropna(subset=[\"Timestamp\", \"Flow Duration\"]).reset_index(drop=True)\n", "    df.sort_values(by=[\"Dst Port\", \"Timestamp\"], inplace=True)\n", "\n", "    # Compute connection rate using a 5-second rolling window\n", "    conn_counts = []\n", "    for _, group in df.groupby(\"Dst Port\"):\n", "        rolling_count = group.rolling(\"5s\", on=\"Timestamp\")[\"Timestamp\"].count()\n", "        conn_counts.append(rolling_count)\n", "    conn_count_series = pd.concat(conn_counts)\n", "    df[\"Conn_Rate\"] = conn_count_series / 5.0\n", "\n", "    # Compute concurrency (extra column, will be dropped later)\n", "    events = []\n", "    for i, row in df.iterrows():\n", "        start_time = row[\"Timestamp\"]\n", "        duration = row[\"Flow Duration\"]\n", "        end_time = start_time + pd.to_timedelta(duration, unit=\"us\")\n", "        events.append((start_time, \"start\", row[\"Dst Port\"], i))\n", "        events.append((end_time, \"end\", row[\"Dst Port\"], i))\n", "    events.sort(key=lambda x: (x[0], x[1]))\n", "    active_flows = {}\n", "    concurrency = pd.Series(0, index=df.index)\n", "    for time, etype, port, idx in events:\n", "        if etype == \"start\":\n", "            concurrency.loc[idx] = active_flows.get(port, 0)\n", "            active_flows[port] = active_flows.get(port, 0) + 1\n", "        else:\n", "            active_flows[port] = active_flows.get(port, 0) - 1\n", "    df[\"Concurrency\"] = concurrency\n", "    return df\n", "\n", "\n", "### Outlier Handling\n", "\n", "\n", "def handle_outliers(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    strategy: str = \"winsorize\",\n", "    threshold: float = 3.0,\n", "    exclude_cols: list = None,\n", ") -> pd.DataFrame:\n", "    \"\"\"Apply outlier handling without removing attack data.\"\"\"\n", "    if exclude_cols is None:\n", "        exclude_cols = [\"Label\", \"Threat\"]\n", "    df_result = df.copy()\n", "    numeric_cols = df_result.select_dtypes(include=[np.number]).columns\n", "    cols_to_process = [col for col in numeric_cols if col not in exclude_cols]\n", "    print(f\"Processing outliers using {strategy} strategy...\")\n", "    for col in cols_to_process:\n", "        z_scores = np.abs(\n", "            (df_result[col] - df_result[col].mean()) / df_result[col].std()\n", "        )\n", "        outliers = z_scores > threshold\n", "        if outliers.sum() == 0:\n", "            continue\n", "        if strategy == \"winsorize\":\n", "            lower_bound = df_result[col].quantile(0.01)\n", "            upper_bound = df_result[col].quantile(0.99)\n", "            df_result.loc[df_result[col] < lower_bound, col] = lower_bound\n", "            df_result.loc[df_result[col] > upper_bound, col] = upper_bound\n", "        elif strategy == \"cap\":\n", "            lower_bound = df_result[col].mean() - (threshold * df_result[col].std())\n", "            upper_bound = df_result[col].mean() + (threshold * df_result[col].std())\n", "            df_result.loc[df_result[col] < lower_bound, col] = lower_bound\n", "            df_result.loc[df_result[col] > upper_bound, col] = upper_bound\n", "        elif strategy == \"log_transform\" and df_result[col].min() >= 0:\n", "            df_result.loc[outliers, col] = np.log1p(df_result.loc[outliers, col])\n", "    return df_result\n", "\n", "\n", "### Scaling Functions\n", "\n", "\n", "def apply_robust_scaling(\n", "    train_df: pd.DataFrame, test_df: pd.DataFrame, use_robust: bool = True\n", ") -> Tuple[pd.DataFrame, pd.DataFrame, object]:\n", "    \"\"\"Apply RobustScaler (or MinMaxScaler) to the feature columns.\"\"\"\n", "    feature_cols = [col for col in train_df.columns if col not in [\"Label\", \"Threat\"]]\n", "    if use_robust:\n", "        print(\"Using RobustScaler for scaling...\")\n", "        scaler = RobustScaler()\n", "    else:\n", "        print(\"Using MinMaxScaler for scaling...\")\n", "        scaler = MinMaxScaler()\n", "    train_df[feature_cols] = scaler.fit_transform(train_df[feature_cols])\n", "    test_df[feature_cols] = scaler.transform(test_df[feature_cols])\n", "    return train_df, test_df, scaler\n", "\n", "\n", "### Final Data Preparation Functions\n", "\n", "\n", "def remove_labels_and_balance(\n", "    df: pd.<PERSON><PERSON>rame, labels_to_remove: list, random_state: int = 42\n", ") -> pd.DataFrame:\n", "    \"\"\"Remove specific labels and balance the dataset.\"\"\"\n", "    initial_count = len(df)\n", "    df = df[~df[\"Label\"].isin(labels_to_remove)]\n", "    removed = initial_count - len(df)\n", "    print(f\"Removed {removed} rows with labels: {labels_to_remove}\")\n", "    min_count = df[\"Label\"].value_counts().min()\n", "    df_balanced = (\n", "        df.groupby(\"Label\")\n", "        .apply(lambda x: x.sample(min_count, random_state=random_state))\n", "        .reset_index(drop=True)\n", "    )\n", "    print(\"Class distribution after balancing:\")\n", "    print(df_balanced[\"Label\"].value_counts())\n", "    return df_balanced\n", "\n", "\n", "def print_dataset_info(df: pd.DataFrame, name: str = \"Dataset\") -> None:\n", "    \"\"\"Print basic information about the dataset.\"\"\"\n", "    print(f\"\\n{name} Information:\")\n", "    print(f\"Shape: {df.shape}\")\n", "    print(\"\\nLabel distribution:\")\n", "    print(df[\"Label\"].value_counts())\n", "    print(\"\\nThreat distribution:\")\n", "    print(df[\"Threat\"].value_counts())\n", "    print(f\"\\nNumber of features: {len(df.columns) - 2}\")\n", "    print(\"\\nMemory usage:\", df.memory_usage().sum() / 1024**2, \"MB\")\n", "\n", "\n", "def save_artifacts(\n", "    train_df: pd.DataFrame,\n", "    test_df: pd.DataFrame,\n", "    class_weights: Dict,\n", "    scaler: Any,\n", "    output_dir: str,\n", ") -> None:\n", "    \"\"\"Save final artifacts to the specified directory.\"\"\"\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    train_df.to_csv(os.path.join(output_dir, \"train_data.csv\"), index=False)\n", "    test_df.to_csv(os.path.join(output_dir, \"test_data.csv\"), index=False)\n", "    joblib.dump(scaler, os.path.join(output_dir, \"scaler.pkl\"))\n", "    with open(os.path.join(output_dir, \"class_weights.json\"), \"w\") as f:\n", "        json.dump(class_weights, f)\n", "    print(f\"All artifacts saved to {output_dir}\")\n", "\n", "\n", "### Top Feature Selection and Data Type Fix\n", "\n", "\n", "def select_top_features(df: pd.DataFrame, top_features: list) -> pd.DataFrame:\n", "    \"\"\"\n", "    Keep only the specified top features (plus Label and Threat if present).\n", "    \"\"\"\n", "    keep_cols = top_features.copy()\n", "    if \"Label\" in df.columns:\n", "        keep_cols.append(\"Label\")\n", "    if \"Threat\" in df.columns:\n", "        keep_cols.append(\"Threat\")\n", "    missing = set(keep_cols) - set(df.columns)\n", "    if missing:\n", "        print(f\"Warning: The following top features are missing: {missing}\")\n", "    return df[[col for col in keep_cols if col in df.columns]]\n", "\n", "\n", "def fix_top_features_data_type(df: pd.DataFrame, top_features: list) -> pd.DataFrame:\n", "    \"\"\"\n", "    Convert the top feature columns to their appropriate data types.\n", "    Mapping based on previous processing:\n", "      - Expected as int: 'Fwd Seg Size Min', 'Dst Port', 'TotLen Fwd Pkts',\n", "        'Init Fwd Win Byts', 'Flow Duration', 'Init Bwd Win Byts', 'Flow IAT Max'\n", "      - Expected as float: 'Conn_Rate', 'Bwd Pkt Len Mean', 'Flow IAT Mean'\n", "    \"\"\"\n", "    dtype_mapping = {\n", "        \"Conn_Rate\": float,\n", "        \"Fwd Seg Size Min\": int,\n", "        \"Bwd Pkt Len Mean\": float,\n", "        \"Dst Port\": int,\n", "        \"TotLen Fwd Pkts\": int,\n", "        \"Init Fwd Win Byts\": int,\n", "        \"Flow Duration\": int,\n", "        \"Flow IAT Mean\": float,\n", "        \"Init Bwd Win Byts\": int,\n", "        \"Flow IAT Max\": int,\n", "    }\n", "    for feature in top_features:\n", "        if feature in df.columns:\n", "            try:\n", "                df[feature] = pd.to_numeric(df[feature], errors=\"coerce\").fillna(0)\n", "                df[feature] = df[feature].astype(dtype_mapping[feature])\n", "            except Exception as e:\n", "                print(f\"Error converting {feature}: {e}\")\n", "    return df\n", "\n", "\n", "### Main Preprocessing Pipeline\n", "\n", "\n", "def main(\n", "    base_path: str,\n", "    output_dir: str,\n", "    dates: list,\n", "    chunksize: int = None,\n", "    test_size: float = 0.2,\n", "    random_state: int = 42,\n", "    outlier_strategy: str = \"winsorize\",\n", "    outlier_threshold: float = 3.0,\n", "    use_robust_scaling: bool = True,\n", "    generate_viz: bool = True,\n", ") -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame, Dict, Any]:\n", "    \"\"\"\n", "    Main pipeline:\n", "      - Load only necessary columns from each CSV.\n", "      - Clean data and compute the essential DoS feature (Conn_Rate).\n", "      - Drop extra columns early.\n", "      - Select only the top 10 features (plus Label/Threat).\n", "      - Fix data types and prepare data for modeling.\n", "      - Generate visualizations of the data.\n", "    \"\"\"\n", "    try:\n", "        print(\"\\nStarting data preprocessing pipeline...\")\n", "        dataframes = load_csv_files(base_path, dates, chunksize=chunksize)\n", "        protected_cols = [\"Conn_Rate\", \"Label\", \"Threat\"]\n", "        \n", "        # Save a sample of original data for before/after comparison in visualizations\n", "        if generate_viz:\n", "            original_sample = None\n", "            for date, df in dataframes.items():\n", "                if len(df) > 0:\n", "                    original_sample = df.sample(min(10000, len(df)), random_state=random_state)\n", "                    break\n", "\n", "        for date, df in dataframes.items():\n", "            print(f\"\\nProcessing {date}...\")\n", "            df = preprocess_initial_columns(df)\n", "            df = clean_infinite_values(df)\n", "            df[\"Timestamp\"] = pd.to_datetime(df[\"Timestamp\"], errors=\"coerce\")\n", "            df = df.dropna(subset=[\"Timestamp\"])\n", "            print(f\"Computing DoS feature for {date}...\")\n", "            df = add_dos_features(df)\n", "            # Drop Timestamp (and extra computed columns like Concurrency) after computing features\n", "            df = drop_timestamp(df)\n", "            df = generate_binary_label(df)\n", "            df = optimize_memory_usage(df)\n", "            df = transform_multi_label(df)\n", "            print(\"\\nBalancing individual file if needed...\")\n", "            df = balance_dataset_if_needed(df)\n", "            dataframes[date] = df\n", "            print_dataset_info(df, f\"{date} dataset\")\n", "\n", "        print(\"\\nCombining all datasets...\")\n", "        combined_df = pd.concat(dataframes.values(), axis=0, ignore_index=True)\n", "        del dataframes\n", "        print_dataset_info(combined_df, \"Combined dataset\")\n", "\n", "        print(\"\\nHandling outliers...\")\n", "        combined_df = handle_outliers(\n", "            combined_df,\n", "            strategy=outlier_strategy,\n", "            threshold=outlier_threshold,\n", "            exclude_cols=protected_cols,\n", "        )\n", "\n", "        # Define the top 10 features\n", "        top_features = [\n", "            \"Conn_Rate\",\n", "            \"Fwd Seg Size Min\",\n", "            \"Bwd Pkt Len Mean\",\n", "            \"Dst Port\",\n", "            \"TotLen Fwd Pkts\",\n", "            \"Init Fwd Win Byts\",\n", "            \"Flow Duration\",\n", "            \"Flow IAT Mean\",\n", "            \"Init Bwd Win Byts\",\n", "            \"Flow IAT Max\",\n", "        ]\n", "        print(\"\\nSelecting top 10 features...\")\n", "        combined_df = select_top_features(combined_df, top_features)\n", "        print(\"Selected features:\", list(combined_df.columns))\n", "\n", "        # Drop any rows missing the top features\n", "        combined_df.dropna(subset=top_features, inplace=True)\n", "        # Fix data types for these features\n", "        combined_df = fix_top_features_data_type(combined_df, top_features)\n", "\n", "        print(\"\\nRemoving unwanted labels and balancing dataset...\")\n", "        combined_df = remove_labels_and_balance(\n", "            combined_df, [\"Infilteration\", \"Web attack\"], random_state=random_state\n", "        )\n", "\n", "        print(\"\\nPreparing data for modeling...\")\n", "        feature_cols = [\n", "            col for col in combined_df.columns if col not in [\"Label\", \"Threat\"]\n", "        ]\n", "        train_df, test_df = train_test_split(\n", "            combined_df,\n", "            test_size=test_size,\n", "            random_state=random_state,\n", "            shuffle=True,\n", "            stratify=combined_df[\"Label\"],\n", "        )\n", "        train_df, test_df, scaler = apply_robust_scaling(\n", "            train_df, test_df, use_robust=use_robust_scaling\n", "        )\n", "\n", "        unique_labels = np.unique(train_df[\"Label\"])\n", "        class_weights = class_weight.compute_class_weight(\n", "            \"balanced\", classes=unique_labels, y=train_df[\"Label\"].values\n", "        )\n", "        class_weights = dict(zip(unique_labels, class_weights))\n", "        print(\"\\nFinal dataset preparation complete!\")\n", "        print_dataset_info(train_df, \"Training dataset\")\n", "        print_dataset_info(test_df, \"Testing dataset\")\n", "        print(\"\\nClass weights:\", class_weights)\n", "        save_artifacts(train_df, test_df, class_weights, scaler, output_dir)\n", "        print(\"\\nNote: Use StratifiedKFold during modeling for robust validation\")\n", "        \n", "        # Generate visualizations\n", "        if generate_viz:\n", "            original_sample_processed = None\n", "            if original_sample is not None:\n", "                try:\n", "                    # Process the original sample through the same pipeline\n", "                    original_sample_processed = preprocess_initial_columns(original_sample)\n", "                    original_sample_processed = clean_infinite_values(original_sample_processed)\n", "                    original_sample_processed = add_dos_features(original_sample_processed)\n", "                    original_sample_processed = generate_binary_label(original_sample_processed)\n", "                    original_sample_processed = transform_multi_label(original_sample_processed)\n", "                    original_sample_processed = select_top_features(original_sample_processed, top_features)\n", "                    original_sample_processed = fix_top_features_data_type(original_sample_processed, top_features)\n", "                except Exception as e:\n", "                    print(f\"Error processing original sample for visualization: {e}\")\n", "                    original_sample_processed = None\n", "            \n", "            # Generate all visualizations\n", "            generate_visualizations(\n", "                train_df=train_df,\n", "                test_df=test_df,\n", "                top_features=top_features,\n", "                output_dir=output_dir,\n", "                original_df=original_sample_processed\n", "            )\n", "        \n", "        return train_df, test_df, class_weights, scaler\n", "    except Exception as e:\n", "        print(f\"\\nError occurred during preprocessing: {e}\")\n", "        raise\n", "\n", "\n", "### Execution Block\n", "\n", "if __name__ == \"__main__\":\n", "    BASE_PATH = r\"C:\\Users\\<USER>\\Desktop\\projects\\big data\\original\"\n", "    OUTPUT_DIR = r\".\\artifacts\"\n", "    DATES = [\n", "        \"02-14-2018\",\n", "        \"02-15-2018\",\n", "        \"02-16-2018\",\n", "        \"02-20-2018\",\n", "        \"02-21-2018\",\n", "        \"02-22-2018\",\n", "        \"02-23-2018\",\n", "        \"02-28-2018\",\n", "        \"03-01-2018\",\n", "        \"03-02-2018\",\n", "    ]\n", "    np.random.seed(42)\n", "    try:\n", "        train_df, test_df, class_weights, scaler = main(\n", "            base_path=BASE_PATH,\n", "            output_dir=OUTPUT_DIR,\n", "            dates=DATES,\n", "            chunksize=100000,\n", "            test_size=0.2,\n", "            random_state=42,\n", "            outlier_strategy=\"winsorize\",\n", "            outlier_threshold=3.0,\n", "            use_robust_scaling=True,\n", "            generate_viz=True,  # Enable visualization generation\n", "        )\n", "        print(\"\\nPreprocessing pipeline completed successfully!\")\n", "    except Exception as e:\n", "        print(f\"\\nFatal error in preprocessing pipeline: {e}\")\n", "        raise"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}